/**
 * @file 组件模块索引 - 统一导出所有组件模块
 * @description 提供组件模块的统一入口和便捷访问
 */

// #region 导入所有组件模块
import { 
    showErrorNotification, 
    showSuccessNotification, 
    showWarningNotification,
    setupLegacyEventHandlers 
} from './event-handlers.js';

import { 
    ExportManager, 
    createExportManager, 
    getExportManager,
    exportAsImage,
    exportAsPDF 
} from './export-manager.js';

import { 
    PrintRangeIndicatorManager,
    A4PreviewZoomManager,
    createPrintRangeIndicatorManager,
    getPrintRangeIndicatorManager,
    getA4PreviewZoomManager,
    initializePreviewManagers 
} from './preview-manager.js';

import { 
    FormProcessor,
    createFormProcessor,
    getFormProcessor 
} from './form-processor.js';
// #endregion

// #region 统一导出
export {
    // 事件处理器
    showErrorNotification,
    showSuccessNotification, 
    showWarningNotification,
    setupLegacyEventHandlers,
    
    // 导出管理器
    ExportManager,
    createExportManager,
    getExportManager,
    exportAsImage,
    exportAsPDF,
    
    // 预览管理器
    PrintRangeIndicatorManager,
    A4PreviewZoomManager,
    createPrintRangeIndicatorManager,
    getPrintRangeIndicatorManager,
    getA4PreviewZoomManager,
    initializePreviewManagers,
    
    // 表单处理器
    FormProcessor,
    createFormProcessor,
    getFormProcessor
};
// #endregion

// #region 组件管理器
/**
 * @class ComponentManager - 组件管理器
 * @description 统一管理所有UI组件的生命周期
 */
export class ComponentManager {
    /**
     * 构造函数 - 初始化组件管理器
     */
    constructor() {
        this.components = new Map();
        this.isInitialized = false;
        
        console.log('📦 组件管理器已创建');
    }

    /**
     * 初始化所有组件
     * @param {Object} options - 初始化选项
     * @returns {Promise<void>}
     */
    async initialize(options = {}) {
        try {
            console.log('🔄 开始初始化组件管理器...');

            // 初始化导出管理器
            const exportManager = getExportManager();
            this.components.set('exportManager', exportManager);

            // 初始化表单处理器
            const formProcessor = getFormProcessor();
            this.components.set('formProcessor', formProcessor);

            // 初始化预览管理器
            initializePreviewManagers();
            const printRangeManager = getPrintRangeIndicatorManager();
            const zoomManager = getA4PreviewZoomManager();
            this.components.set('printRangeManager', printRangeManager);
            this.components.set('zoomManager', zoomManager);

            this.isInitialized = true;
            console.log('✅ 组件管理器初始化完成');

        } catch (error) {
            console.error('❌ 组件管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 获取组件实例
     * @param {string} name - 组件名称
     * @returns {*} 组件实例
     */
    getComponent(name) {
        return this.components.get(name);
    }

    /**
     * 获取所有组件
     * @returns {Map} 组件映射
     */
    getAllComponents() {
        return new Map(this.components);
    }

    /**
     * 检查组件是否存在
     * @param {string} name - 组件名称
     * @returns {boolean} 是否存在
     */
    hasComponent(name) {
        return this.components.has(name);
    }

    /**
     * 销毁所有组件
     */
    destroy() {
        try {
            console.log('🔄 开始销毁组件管理器...');

            // 销毁各个组件
            for (const [name, component] of this.components) {
                if (component && typeof component.destroy === 'function') {
                    try {
                        component.destroy();
                        console.log(`✅ 组件 ${name} 已销毁`);
                    } catch (error) {
                        console.error(`❌ 组件 ${name} 销毁失败:`, error);
                    }
                }
            }

            this.components.clear();
            this.isInitialized = false;
            
            console.log('✅ 组件管理器销毁完成');

        } catch (error) {
            console.error('❌ 组件管理器销毁失败:', error);
        }
    }

    /**
     * 获取组件状态
     * @returns {Object} 组件状态信息
     */
    getStatus() {
        const status = {
            isInitialized: this.isInitialized,
            componentCount: this.components.size,
            components: {}
        };

        for (const [name, component] of this.components) {
            status.components[name] = {
                exists: !!component,
                hasDestroyMethod: typeof component?.destroy === 'function',
                type: component?.constructor?.name || 'Unknown'
            };
        }

        return status;
    }
}
// #endregion

// #region 全局组件管理器实例
let globalComponentManager = null;

/**
 * 获取全局组件管理器实例
 * @returns {ComponentManager} 全局组件管理器
 */
export function getComponentManager() {
    if (!globalComponentManager) {
        globalComponentManager = new ComponentManager();
    }
    return globalComponentManager;
}

/**
 * 初始化所有组件
 * @param {Object} options - 初始化选项
 * @returns {Promise<ComponentManager>} 组件管理器实例
 */
export async function initializeComponents(options = {}) {
    const manager = getComponentManager();
    await manager.initialize(options);
    return manager;
}
// #endregion

// #region 便捷函数
/**
 * 快速获取导出管理器
 * @returns {ExportManager} 导出管理器实例
 */
export function getExport() {
    return getExportManager();
}

/**
 * 快速获取表单处理器
 * @returns {FormProcessor} 表单处理器实例
 */
export function getForm() {
    return getFormProcessor();
}

/**
 * 快速获取预览缩放管理器
 * @returns {Object} 预览缩放管理器
 */
export function getZoom() {
    return getA4PreviewZoomManager();
}

/**
 * 快速获取打印范围管理器
 * @returns {PrintRangeIndicatorManager} 打印范围管理器实例
 */
export function getPrintRange() {
    return getPrintRangeIndicatorManager();
}
// #endregion
