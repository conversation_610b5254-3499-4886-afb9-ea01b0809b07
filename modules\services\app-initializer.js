/**
 * @file 应用初始化器 - 统一应用程序启动和初始化服务模块
 * <AUTHOR> Team
 * @description 
 * 统一的应用初始化器，整合了原js/services/app-initializer.js的功能
 * 负责应用程序的启动和初始化流程管理
 * 
 * 功能特性：
 * - 应用启动流程管理
 * - 依赖检查和加载
 * - 环境检测和配置
 * - 错误恢复和降级处理
 * - 启动性能监控
 * - 兼容性检查
 */

// #region 导入依赖
import { getLogger } from '../core/logger.js';
import { getEventBus } from '../core/events.js';
// #endregion

// #region 应用初始化器类定义
/**
 * @class AppInitializer - 统一应用初始化器类
 * @description 管理应用程序的启动和初始化流程
 */
export class AppInitializer {
    /**
     * 构造函数 - 初始化应用初始化器
     * @param {Object} options - 初始化选项
     */
    constructor(options = {}) {
        this.logger = getLogger();
        this.eventBus = getEventBus();
        
        // 核心依赖
        this.config = options.config;
        
        // 初始化状态
        this.isInitialized = false;
        this.initializationSteps = [];
        this.currentStep = 0;
        this.startTime = null;
        
        // 依赖检查结果
        this.dependencies = {
            required: new Map(),
            optional: new Map()
        };
        
        // 环境信息
        this.environment = {
            browser: null,
            version: null,
            features: new Map(),
            performance: {}
        };
        
        // 错误收集
        this.initErrors = [];
        this.warnings = [];
        
        // 初始化配置
        this.initConfig = {
            enablePerformanceMonitoring: true,
            enableFeatureDetection: true,
            strictMode: false,
            timeout: 30000,
            ...options.initConfig
        };
        
        this.logger.info('AppInitializer', 'constructor', '应用初始化器已创建');
    }

    /**
     * 初始化应用程序
     * @returns {Promise<Object>} 初始化结果
     */
    async initialize() {
        this.startTime = Date.now();
        
        try {
            this.logger.info('AppInitializer', 'initialize', '开始应用程序初始化...');
            
            // 定义初始化步骤
            this._defineInitializationSteps();
            
            // 执行初始化步骤
            const result = await this._executeInitializationSteps();
            
            this.isInitialized = true;
            
            // 触发初始化完成事件
            this.eventBus.emit('app:initialized', {
                duration: Date.now() - this.startTime,
                steps: this.initializationSteps.length,
                errors: this.initErrors.length,
                warnings: this.warnings.length,
                timestamp: new Date()
            });
            
            this.logger.info('AppInitializer', 'initialize', '应用程序初始化完成');
            return result;
            
        } catch (error) {
            this.initErrors.push({
                step: 'initialization',
                error: error.message,
                timestamp: new Date()
            });
            
            // 触发初始化失败事件
            this.eventBus.emit('app:init-failed', {
                error: error.message,
                errors: this.initErrors,
                timestamp: new Date()
            });
            
            this.logger.error('AppInitializer', 'initialize', '应用程序初始化失败', error);
            throw error;
        }
    }

    /**
     * 检查浏览器兼容性
     * @returns {Object} 兼容性检查结果
     */
    checkBrowserCompatibility() {
        const compatibility = {
            isSupported: true,
            issues: [],
            recommendations: []
        };
        
        // 检测浏览器信息
        this._detectBrowser();
        
        // 检查必需的API
        const requiredAPIs = [
            'localStorage',
            'sessionStorage',
            'fetch',
            'Promise',
            'Map',
            'Set',
            'addEventListener'
        ];
        
        for (const api of requiredAPIs) {
            if (!this._checkAPI(api)) {
                compatibility.isSupported = false;
                compatibility.issues.push(`缺少必需的API: ${api}`);
            }
        }
        
        // 检查可选的API
        const optionalAPIs = [
            'IntersectionObserver',
            'ResizeObserver',
            'requestIdleCallback',
            'WebAssembly'
        ];
        
        for (const api of optionalAPIs) {
            if (!this._checkAPI(api)) {
                compatibility.recommendations.push(`建议升级浏览器以支持: ${api}`);
            }
        }
        
        // 检查浏览器版本
        if (this.environment.browser) {
            const minVersions = {
                chrome: 70,
                firefox: 65,
                safari: 12,
                edge: 79
            };
            
            const browserName = this.environment.browser.toLowerCase();
            const minVersion = minVersions[browserName];
            
            if (minVersion && this.environment.version < minVersion) {
                compatibility.issues.push(
                    `浏览器版本过低，建议升级到 ${browserName} ${minVersion} 或更高版本`
                );
            }
        }
        
        return compatibility;
    }

    /**
     * 获取初始化状态
     * @returns {Object} 初始化状态信息
     */
    getInitializationStatus() {
        return {
            isInitialized: this.isInitialized,
            currentStep: this.currentStep,
            totalSteps: this.initializationSteps.length,
            progress: this.initializationSteps.length > 0 ? 
                (this.currentStep / this.initializationSteps.length) * 100 : 0,
            duration: this.startTime ? Date.now() - this.startTime : 0,
            errors: this.initErrors.length,
            warnings: this.warnings.length,
            environment: this.environment
        };
    }

    /**
     * 获取环境信息
     * @returns {Object} 环境信息
     */
    getEnvironmentInfo() {
        return {
            ...this.environment,
            dependencies: {
                required: Object.fromEntries(this.dependencies.required),
                optional: Object.fromEntries(this.dependencies.optional)
            },
            errors: this.initErrors,
            warnings: this.warnings
        };
    }

    /**
     * 获取服务统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            isInitialized: this.isInitialized,
            initializationTime: this.startTime ? Date.now() - this.startTime : 0,
            stepsCompleted: this.currentStep,
            totalSteps: this.initializationSteps.length,
            errorCount: this.initErrors.length,
            warningCount: this.warnings.length,
            browserSupported: this.environment.browser !== null,
            dependenciesLoaded: this.dependencies.required.size + this.dependencies.optional.size
        };
    }

    // #region 私有方法
    /**
     * 定义初始化步骤
     * @private
     */
    _defineInitializationSteps() {
        this.initializationSteps = [
            {
                name: 'environment-detection',
                description: '环境检测',
                handler: this._detectEnvironment.bind(this),
                required: true
            },
            {
                name: 'browser-compatibility',
                description: '浏览器兼容性检查',
                handler: this._checkBrowserCompatibility.bind(this),
                required: true
            },
            {
                name: 'dependency-check',
                description: '依赖检查',
                handler: this._checkDependencies.bind(this),
                required: true
            },
            {
                name: 'config-validation',
                description: '配置验证',
                handler: this._validateConfiguration.bind(this),
                required: true
            },
            {
                name: 'storage-check',
                description: '存储检查',
                handler: this._checkStorage.bind(this),
                required: true
            },
            {
                name: 'performance-baseline',
                description: '性能基准测试',
                handler: this._establishPerformanceBaseline.bind(this),
                required: false
            },
            {
                name: 'feature-detection',
                description: '功能特性检测',
                handler: this._detectFeatures.bind(this),
                required: false
            }
        ];
    }

    /**
     * 执行初始化步骤
     * @returns {Promise<Object>} 执行结果
     * @private
     */
    async _executeInitializationSteps() {
        const results = {};

        for (let i = 0; i < this.initializationSteps.length; i++) {
            const step = this.initializationSteps[i];
            this.currentStep = i + 1;

            try {
                this.logger.info('AppInitializer', '_executeInitializationSteps',
                    `执行步骤 ${this.currentStep}/${this.initializationSteps.length}: ${step.description}`);

                // 触发步骤开始事件
                this.eventBus.emit('app:init-step', {
                    step: step.name,
                    description: step.description,
                    current: this.currentStep,
                    total: this.initializationSteps.length,
                    timestamp: new Date()
                });

                // 执行步骤
                const stepResult = await step.handler();
                results[step.name] = stepResult;

                this.logger.info('AppInitializer', '_executeInitializationSteps', `步骤完成: ${step.description}`);

            } catch (error) {
                const errorInfo = {
                    step: step.name,
                    error: error.message,
                    timestamp: new Date()
                };

                if (step.required) {
                    this.initErrors.push(errorInfo);
                    throw new Error(`必需步骤失败: ${step.description} - ${error.message}`);
                } else {
                    this.warnings.push(errorInfo);
                    this.logger.warn('AppInitializer', '_executeInitializationSteps',
                        `可选步骤失败: ${step.description}`, error);
                    results[step.name] = { error: error.message };
                }
            }
        }

        return results;
    }

    /**
     * 检测环境
     * @returns {Object} 环境信息
     * @private
     */
    async _detectEnvironment() {
        // 检测运行环境
        const env = {
            userAgent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            cookieEnabled: navigator.cookieEnabled,
            onLine: navigator.onLine,
            screen: {
                width: screen.width,
                height: screen.height,
                colorDepth: screen.colorDepth
            },
            viewport: {
                width: window.innerWidth,
                height: window.innerHeight
            },
            timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
            timestamp: new Date()
        };

        // 更新环境信息
        Object.assign(this.environment, env);

        return env;
    }

    /**
     * 检查浏览器兼容性
     * @returns {Object} 兼容性检查结果
     * @private
     */
    async _checkBrowserCompatibility() {
        const compatibility = this.checkBrowserCompatibility();

        if (!compatibility.isSupported) {
            throw new Error(`浏览器不兼容: ${compatibility.issues.join(', ')}`);
        }

        // 记录警告
        for (const recommendation of compatibility.recommendations) {
            this.warnings.push({
                step: 'browser-compatibility',
                warning: recommendation,
                timestamp: new Date()
            });
        }

        return compatibility;
    }

    /**
     * 检查依赖
     * @returns {Object} 依赖检查结果
     * @private
     */
    async _checkDependencies() {
        // 必需依赖
        const requiredDeps = [
            { name: 'EventTarget', check: () => typeof EventTarget !== 'undefined' },
            { name: 'CustomEvent', check: () => typeof CustomEvent !== 'undefined' },
            { name: 'MutationObserver', check: () => typeof MutationObserver !== 'undefined' }
        ];

        // 可选依赖
        const optionalDeps = [
            { name: 'html2canvas', check: () => typeof window.html2canvas !== 'undefined' },
            { name: 'jsPDF', check: () => typeof window.jsPDF !== 'undefined' },
            { name: 'JSZip', check: () => typeof window.JSZip !== 'undefined' }
        ];

        // 检查必需依赖
        for (const dep of requiredDeps) {
            const isAvailable = dep.check();
            this.dependencies.required.set(dep.name, isAvailable);

            if (!isAvailable) {
                throw new Error(`缺少必需依赖: ${dep.name}`);
            }
        }

        // 检查可选依赖
        for (const dep of optionalDeps) {
            const isAvailable = dep.check();
            this.dependencies.optional.set(dep.name, isAvailable);

            if (!isAvailable) {
                this.warnings.push({
                    step: 'dependency-check',
                    warning: `缺少可选依赖: ${dep.name}`,
                    timestamp: new Date()
                });
            }
        }

        return {
            required: Object.fromEntries(this.dependencies.required),
            optional: Object.fromEntries(this.dependencies.optional)
        };
    }

    /**
     * 验证配置
     * @returns {Object} 配置验证结果
     * @private
     */
    async _validateConfiguration() {
        // 检查配置管理器
        if (!this.config) {
            throw new Error('配置管理器未初始化');
        }

        // 验证关键配置
        const requiredConfigs = [
            'app.name',
            'app.version',
            'nlp.enabled',
            'document.defaultType',
            'export.defaultFormat'
        ];

        const missingConfigs = [];

        for (const configPath of requiredConfigs) {
            const value = this.config.get(configPath);
            if (value === undefined || value === null) {
                missingConfigs.push(configPath);
            }
        }

        if (missingConfigs.length > 0) {
            throw new Error(`缺少必需配置: ${missingConfigs.join(', ')}`);
        }

        return {
            valid: true,
            configs: requiredConfigs.map(path => ({
                path,
                value: this.config.get(path)
            }))
        };
    }

    /**
     * 检查存储
     * @returns {Object} 存储检查结果
     * @private
     */
    async _checkStorage() {
        const storage = {
            localStorage: false,
            sessionStorage: false,
            indexedDB: false
        };

        // 检查localStorage
        try {
            const testKey = 'smartoffice-storage-test';
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            storage.localStorage = true;
        } catch (error) {
            this.logger.warn('AppInitializer', '_checkStorage', 'localStorage不可用', error);
        }

        // 检查sessionStorage
        try {
            const testKey = 'smartoffice-session-test';
            sessionStorage.setItem(testKey, 'test');
            sessionStorage.removeItem(testKey);
            storage.sessionStorage = true;
        } catch (error) {
            this.logger.warn('AppInitializer', '_checkStorage', 'sessionStorage不可用', error);
        }

        // 检查IndexedDB
        try {
            storage.indexedDB = 'indexedDB' in window;
        } catch (error) {
            this.logger.warn('AppInitializer', '_checkStorage', 'IndexedDB不可用', error);
        }

        // 至少需要localStorage
        if (!storage.localStorage) {
            throw new Error('localStorage不可用，应用无法正常运行');
        }

        return storage;
    }

    /**
     * 建立性能基准
     * @returns {Object} 性能基准信息
     * @private
     */
    async _establishPerformanceBaseline() {
        const performance = {
            memory: {},
            timing: {},
            connection: {}
        };

        // 内存信息
        if (window.performance && window.performance.memory) {
            performance.memory = {
                usedJSHeapSize: window.performance.memory.usedJSHeapSize,
                totalJSHeapSize: window.performance.memory.totalJSHeapSize,
                jsHeapSizeLimit: window.performance.memory.jsHeapSizeLimit
            };
        }

        // 时间信息
        if (window.performance && window.performance.timing) {
            const timing = window.performance.timing;
            performance.timing = {
                navigationStart: timing.navigationStart,
                domContentLoadedEventEnd: timing.domContentLoadedEventEnd,
                loadEventEnd: timing.loadEventEnd,
                domComplete: timing.domComplete
            };
        }

        // 连接信息
        if (navigator.connection) {
            performance.connection = {
                effectiveType: navigator.connection.effectiveType,
                downlink: navigator.connection.downlink,
                rtt: navigator.connection.rtt
            };
        }

        // 更新环境性能信息
        this.environment.performance = performance;

        return performance;
    }

    /**
     * 检测功能特性
     * @returns {Object} 功能特性信息
     * @private
     */
    async _detectFeatures() {
        const features = new Map();

        // 检测各种功能特性
        features.set('webgl', this._checkWebGL());
        features.set('webworkers', typeof Worker !== 'undefined');
        features.set('serviceworkers', 'serviceWorker' in navigator);
        features.set('geolocation', 'geolocation' in navigator);
        features.set('notifications', 'Notification' in window);
        features.set('fullscreen', document.fullscreenEnabled || document.webkitFullscreenEnabled);
        features.set('clipboard', 'clipboard' in navigator);
        features.set('share', 'share' in navigator);

        // 更新环境特性信息
        this.environment.features = features;

        return Object.fromEntries(features);
    }

    /**
     * 检测浏览器
     * @private
     */
    _detectBrowser() {
        const userAgent = navigator.userAgent;

        if (userAgent.includes('Chrome')) {
            this.environment.browser = 'Chrome';
            const match = userAgent.match(/Chrome\/(\d+)/);
            this.environment.version = match ? parseInt(match[1]) : 0;
        } else if (userAgent.includes('Firefox')) {
            this.environment.browser = 'Firefox';
            const match = userAgent.match(/Firefox\/(\d+)/);
            this.environment.version = match ? parseInt(match[1]) : 0;
        } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
            this.environment.browser = 'Safari';
            const match = userAgent.match(/Version\/(\d+)/);
            this.environment.version = match ? parseInt(match[1]) : 0;
        } else if (userAgent.includes('Edge')) {
            this.environment.browser = 'Edge';
            const match = userAgent.match(/Edge\/(\d+)/);
            this.environment.version = match ? parseInt(match[1]) : 0;
        }
    }

    /**
     * 检查API可用性
     * @param {string} api - API名称
     * @returns {boolean} 是否可用
     * @private
     */
    _checkAPI(api) {
        switch (api) {
            case 'localStorage':
                return typeof Storage !== 'undefined' && 'localStorage' in window;
            case 'sessionStorage':
                return typeof Storage !== 'undefined' && 'sessionStorage' in window;
            case 'fetch':
                return typeof fetch !== 'undefined';
            case 'Promise':
                return typeof Promise !== 'undefined';
            case 'Map':
                return typeof Map !== 'undefined';
            case 'Set':
                return typeof Set !== 'undefined';
            case 'addEventListener':
                return 'addEventListener' in window;
            case 'IntersectionObserver':
                return typeof IntersectionObserver !== 'undefined';
            case 'ResizeObserver':
                return typeof ResizeObserver !== 'undefined';
            case 'requestIdleCallback':
                return typeof requestIdleCallback !== 'undefined';
            case 'WebAssembly':
                return typeof WebAssembly !== 'undefined';
            default:
                return false;
        }
    }

    /**
     * 检查WebGL支持
     * @returns {boolean} 是否支持WebGL
     * @private
     */
    _checkWebGL() {
        try {
            const canvas = document.createElement('canvas');
            return !!(window.WebGLRenderingContext &&
                     (canvas.getContext('webgl') || canvas.getContext('experimental-webgl')));
        } catch (e) {
            return false;
        }
    }
    // #endregion

    /**
     * 销毁应用初始化器
     */
    destroy() {
        // 清理数据
        this.dependencies.required.clear();
        this.dependencies.optional.clear();
        this.environment.features.clear();
        this.initializationSteps = [];
        this.initErrors = [];
        this.warnings = [];
        this.isInitialized = false;

        this.logger.info('AppInitializer', 'destroy', '应用初始化器已销毁');
    }
}

// #region 导出
export default AppInitializer;

// 便捷函数
export function createAppInitializer(options = {}) {
    return new AppInitializer(options);
}
// #endregion
