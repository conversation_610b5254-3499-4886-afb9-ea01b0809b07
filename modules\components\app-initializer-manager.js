/**
 * @file 应用初始化管理器 - 统一的应用启动和初始化流程
 * 负责应用的启动、错误处理、回退机制等
 */

(function() {
    'use strict';

    /**
     * 应用初始化管理器类
     * @class AppInitializerManager
     */
    class AppInitializerManager {
        constructor() {
            this.isInitialized = false;
            this.initializationPromise = null;
            this.managers = new Map();
            this.stats = {
                documentsCreated: 0,
                exportsCompleted: 0,
                nlpProcessed: 0,
                startTime: Date.now()
            };
        }

        /**
         * 创建增强的SmartOffice应用
         * @function createEnhancedSmartOfficeApp
         * @param {Object} config - 应用配置
         * @returns {Promise<Object>} 增强的应用实例
         */
        async createEnhancedSmartOfficeApp(config = {}) {
            if (window.SmartOffice && window.SmartOffice.Logger) {
                window.SmartOffice.Logger.info('AppInitializerManager', 'createEnhancedSmartOfficeApp', '🔄 创建增强SmartOffice应用');
            }
            
            // 创建增强的应用实例
            const enhancedApp = {
                config: config,
                isInitialized: false,
                managers: this.managers,
                stats: this.stats,
                
                // 应用信息
                getAppInfo: () => this.getAppInfo(),
                
                // 获取性能信息
                _getPerformanceInfo: () => this._getPerformanceInfo(),
                
                // 清理内存和资源
                cleanup: () => this.cleanup(),
                
                // 初始化应用
                init: () => this.init(),
                
                // 获取管理器
                getManager: (name) => this.managers.get(name),
                
                // 设置管理器
                setManager: (name, manager) => this.managers.set(name, manager),
                
                // 启动应用
                start: () => this.startApp()
            };
            
            return enhancedApp;
        }

        /**
         * 获取应用信息
         * @function getAppInfo
         * @returns {Object} 应用信息
         */
        getAppInfo() {
            // 获取性能信息
            const performanceInfo = this._getPerformanceInfo();
            
            return {
                version: '2.0.0-enhanced',
                name: 'SmartOffice (Enhanced Inline)',
                mode: 'enhanced-inline',
                managersCount: this.managers.size,
                isInitialized: this.isInitialized,
                timestamp: new Date().toISOString(),
                features: {
                    nlp: 'gemini-only',
                    export: 'full',
                    offline: false,
                    modules: 'inline',
                    batch: true,
                    workflow: true,
                    plugins: true,
                    renderers: true,
                    templates: true,
                    exporters: true
                },
                stats: this.stats,
                performance: performanceInfo
            };
        }

        /**
         * 获取性能信息
         * @function _getPerformanceInfo
         * @returns {Object} 性能信息
         * @private
         */
        _getPerformanceInfo() {
            const memoryInfo = performance.memory ? {
                usedJSHeapSize: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + ' MB',
                totalJSHeapSize: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + ' MB',
                jsHeapSizeLimit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + ' MB'
            } : null;
            
            return {
                uptime: Date.now() - this.stats.startTime,
                memory: memoryInfo,
                networkStatus: navigator.onLine ? 'online' : 'offline',
                userAgent: navigator.userAgent,
                language: navigator.language,
                platform: navigator.platform
            };
        }

        /**
         * 清理内存和资源
         * @function cleanup
         */
        cleanup() {
            if (window.SmartOffice && window.SmartOffice.Logger) {
                window.SmartOffice.Logger.info('AppInitializerManager', 'cleanup', '🧹 开始清理应用资源');
            }
            
            // 清理管理器
            for (const [name, manager] of this.managers) {
                if (manager && typeof manager.cleanup === 'function') {
                    try {
                        manager.cleanup();
                        if (window.SmartOffice && window.SmartOffice.Logger) {
                            window.SmartOffice.Logger.debug('AppInitializerManager', 'cleanup', `管理器 ${name} 已清理`);
                        }
                    } catch (error) {
                        if (window.SmartOffice && window.SmartOffice.Logger) {
                            window.SmartOffice.Logger.error('AppInitializerManager', 'cleanup', `清理管理器 ${name} 失败`, { error: error.message });
                        }
                    }
                }
            }
            
            // 清理统计信息
            this.stats = {
                documentsCreated: 0,
                exportsCompleted: 0,
                nlpProcessed: 0,
                startTime: Date.now()
            };
            
            // 强制垃圾回收（如果可用）
            if (window.gc) {
                window.gc();
            }
            
            if (window.SmartOffice && window.SmartOffice.Logger) {
                window.SmartOffice.Logger.info('AppInitializerManager', 'cleanup', '✅ 应用资源清理完成');
            }
        }

        /**
         * 初始化应用
         * @function init
         * @returns {Promise<Object>} 应用实例
         */
        async init() {
            if (this.isInitialized) {
                return this;
            }
            
            if (this.initializationPromise) {
                return this.initializationPromise;
            }
            
            this.initializationPromise = this._performInitialization();
            return this.initializationPromise;
        }

        /**
         * 执行初始化
         * @function _performInitialization
         * @returns {Promise<Object>} 应用实例
         * @private
         */
        async _performInitialization() {
            if (window.SmartOffice && window.SmartOffice.Logger) {
                window.SmartOffice.Logger.info('AppInitializerManager', 'init', '🔄 开始初始化增强应用');
            }
            
            try {
                // 初始化核心管理器
                if (window.SmartOffice && window.SmartOffice.Logger) {
                    window.SmartOffice.Logger.debug('AppInitializerManager', 'init', '初始化核心管理器');
                }
                
                // 首先初始化统一渲染引擎
                this.createUnifiedRenderEngine();
                if (window.SmartOffice && window.SmartOffice.Logger) {
                    window.SmartOffice.Logger.info('AppInitializerManager', 'init', '✅ 统一渲染引擎初始化完成');
                }
                
                // 初始化其他核心管理器
                this.createStateManager();
                this.createEventManager();
                this.createRenderersManager(); // 兼容性包装器
                this.createTemplatesManager();
                
                if (window.SmartOffice && window.SmartOffice.Logger) {
                    window.SmartOffice.Logger.info('AppInitializerManager', 'init', '✅ 核心管理器初始化完成', {
                        managersCount: this.managers.size
                    });
                }
                
                this.isInitialized = true;
                return this;
                
            } catch (error) {
                if (window.SmartOffice && window.SmartOffice.Logger) {
                    window.SmartOffice.Logger.error('AppInitializerManager', 'init', '❌ 应用初始化失败', { error: error.message });
                }
                throw error;
            }
        }

        /**
         * 启动应用
         * @function startApp
         * @returns {Promise<Object>} 应用实例
         */
        async startApp() {
            try {
                // 确保应用已初始化
                await this.init();
                
                // 初始化组件管理器
                await this.initializeComponentManagers();
                
                if (window.SmartOffice && window.SmartOffice.Logger) {
                    window.SmartOffice.Logger.info('AppInitializerManager', 'startApp', '✅ 应用启动完成');
                }
                
                return this;
                
            } catch (error) {
                if (window.SmartOffice && window.SmartOffice.Logger) {
                    window.SmartOffice.Logger.error('AppInitializerManager', 'startApp', '❌ 应用启动失败', { error: error.message });
                }
                throw error;
            }
        }

        /**
         * 初始化组件管理器
         * @function initializeComponentManagers
         * @private
         */
        async initializeComponentManagers() {
            // 初始化通知管理器
            if (window.SmartOffice?.Components?.NotificationManager) {
                const notificationManager = new window.SmartOffice.Components.NotificationManager();
                this.managers.set('notification', notificationManager);
                window.SmartOffice.Components.notificationManager = notificationManager;
            }
            
            // 初始化API状态管理器
            if (window.SmartOffice?.Components?.ApiStatusManager) {
                const apiStatusManager = new window.SmartOffice.Components.ApiStatusManager();
                this.managers.set('apiStatus', apiStatusManager);
                window.SmartOffice.Components.apiStatusManager = apiStatusManager;
            }
            
            // 初始化表单数据管理器
            if (window.SmartOffice?.Components?.FormDataManager) {
                const formDataManager = new window.SmartOffice.Components.FormDataManager();
                this.managers.set('formData', formDataManager);
                window.SmartOffice.Components.formDataManager = formDataManager;
            }
            
            // 初始化资源管理器
            if (window.SmartOffice?.Utils?.InlineResourceManager) {
                const resourceManager = new window.SmartOffice.Utils.InlineResourceManager();
                await resourceManager.init();
                this.managers.set('resource', resourceManager);
                window.SmartOffice.Utils.inlineResourceManager = resourceManager;
            }
        }

        /**
         * 创建统一渲染引擎
         * @function createUnifiedRenderEngine
         */
        createUnifiedRenderEngine() {
            // 创建简化的渲染引擎
            const renderEngine = {
                name: 'UnifiedRenderEngine',
                version: '2.0.0',
                render: function(template, data) {
                    // 简化的渲染逻辑
                    return `渲染结果: ${template} with ${JSON.stringify(data)}`;
                }
            };
            
            this.managers.set('renderEngine', renderEngine);
        }

        /**
         * 创建状态管理器
         * @function createStateManager
         */
        createStateManager() {
            const stateManager = {
                name: 'StateManager',
                state: {},
                setState: function(key, value) {
                    this.state[key] = value;
                },
                getState: function(key) {
                    return this.state[key];
                }
            };
            
            this.managers.set('state', stateManager);
        }

        /**
         * 创建事件管理器
         * @function createEventManager
         */
        createEventManager() {
            const eventManager = {
                name: 'EventManager',
                listeners: new Map(),
                on: function(event, callback) {
                    if (!this.listeners.has(event)) {
                        this.listeners.set(event, []);
                    }
                    this.listeners.get(event).push(callback);
                },
                emit: function(event, data) {
                    const callbacks = this.listeners.get(event) || [];
                    callbacks.forEach(callback => callback(data));
                }
            };
            
            this.managers.set('event', eventManager);
        }

        /**
         * 创建渲染器管理器
         * @function createRenderersManager
         */
        createRenderersManager() {
            const renderersManager = {
                name: 'RenderersManager',
                renderers: new Map()
            };
            
            this.managers.set('renderers', renderersManager);
        }

        /**
         * 创建模板管理器
         * @function createTemplatesManager
         */
        createTemplatesManager() {
            const templatesManager = {
                name: 'TemplatesManager',
                templates: new Map()
            };
            
            this.managers.set('templates', templatesManager);
        }
    }

    // 导出到全局命名空间
    if (!window.SmartOffice) {
        window.SmartOffice = {};
    }
    if (!window.SmartOffice.Components) {
        window.SmartOffice.Components = {};
    }

    window.SmartOffice.Components.AppInitializerManager = AppInitializerManager;

    // 兼容性：导出全局函数
    window.createEnhancedSmartOfficeApp = async function(config) {
        const appInitializer = new AppInitializerManager();
        return await appInitializer.createEnhancedSmartOfficeApp(config);
    };

})();
