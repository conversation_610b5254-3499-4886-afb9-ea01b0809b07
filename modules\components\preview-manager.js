/**
 * @file 预览管理器模块 - 从index.html提取的预览和缩放功能
 * @description 提供文档预览、缩放控制和打印范围指示功能
 */

import { getLogger } from '../core/logger.js';

// #region 全局变量
const pageLogger = getLogger();
// #endregion

// #region 打印范围指示器管理器
/**
 * @class PrintRangeIndicatorManager - 打印范围指示器管理器
 * @description 管理预览模块中的打印范围显示功能，确保用户能清楚看到实际打印区域
 */
export class PrintRangeIndicatorManager {
    /**
     * 构造函数 - 初始化打印范围指示器管理器
     */
    constructor() {
        this.isVisible = false; // 打印范围指示器是否可见
        this.indicator = null; // 打印范围指示器元素
        this.toggleButton = null; // 切换按钮
        
        pageLogger.info('PrintRangeIndicatorManager', 'constructor', '📏 打印范围指示器管理器已创建');
        this._initialize();
    }

    /**
     * 初始化打印范围指示器
     * @private
     */
    _initialize() {
        try {
            // 获取指示器元素
            this.indicator = document.getElementById('print-range-indicator');
            this.toggleButton = document.getElementById('print-range-toggle-btn');
            
            if (!this.indicator) {
                pageLogger.warn('PrintRangeIndicatorManager', '_initialize', '找不到打印范围指示器元素');
                return;
            }

            // 设置切换按钮事件
            if (this.toggleButton) {
                this.toggleButton.addEventListener('click', () => {
                    this.toggle();
                });
            }

            // 初始化指示器样式
            this._setupIndicatorStyles();
            
            pageLogger.info('PrintRangeIndicatorManager', '_initialize', '✅ 打印范围指示器初始化完成');
            
        } catch (error) {
            pageLogger.error('PrintRangeIndicatorManager', '_initialize', '❌ 打印范围指示器初始化失败', error);
        }
    }

    /**
     * 切换打印范围指示器显示状态
     */
    toggle() {
        if (this.isVisible) {
            this.hide();
        } else {
            this.show();
        }
    }

    /**
     * 显示打印范围指示器
     */
    show() {
        if (!this.indicator) return;
        
        this.indicator.classList.remove('hidden');
        this.isVisible = true;
        
        // 更新按钮状态
        if (this.toggleButton) {
            this.toggleButton.classList.add('bg-blue-200');
            this.toggleButton.title = '隐藏打印范围';
        }
        
        // 更新指示器内容
        this._updateIndicatorContent();
        
        pageLogger.debug('PrintRangeIndicatorManager', 'show', '打印范围指示器已显示');
    }

    /**
     * 隐藏打印范围指示器
     */
    hide() {
        if (!this.indicator) return;
        
        this.indicator.classList.add('hidden');
        this.isVisible = false;
        
        // 更新按钮状态
        if (this.toggleButton) {
            this.toggleButton.classList.remove('bg-blue-200');
            this.toggleButton.title = '显示打印范围';
        }
        
        pageLogger.debug('PrintRangeIndicatorManager', 'hide', '打印范围指示器已隐藏');
    }

    /**
     * 更新指示器内容
     * @private
     */
    _updateIndicatorContent() {
        if (!this.indicator) return;

        // 获取文档容器尺寸
        const documentContainer = document.getElementById('document-container');
        if (!documentContainer) return;

        const containerRect = documentContainer.getBoundingClientRect();
        
        // 更新边距标签
        const marginLabels = {
            top: this.indicator.querySelector('.margin-label-top'),
            bottom: this.indicator.querySelector('.margin-label-bottom'),
            left: this.indicator.querySelector('.margin-label-left'),
            right: this.indicator.querySelector('.margin-label-right')
        };

        // 更新标签内容
        if (marginLabels.top) marginLabels.top.textContent = '上边距 20mm';
        if (marginLabels.bottom) marginLabels.bottom.textContent = '下边距 15mm';
        if (marginLabels.left) marginLabels.left.textContent = '左边距 10mm';
        if (marginLabels.right) marginLabels.right.textContent = '右边距 10mm';

        // 更新打印区域尺寸标签
        const printAreaLabel = this.indicator.querySelector('.print-area-size-label');
        if (printAreaLabel) {
            const width = Math.round(containerRect.width);
            const height = Math.round(containerRect.height);
            printAreaLabel.textContent = `打印区域: ${width}×${height}px`;
        }

        // 更新页面信息标签
        const pageInfoLabel = this.indicator.querySelector('.page-info-label');
        if (pageInfoLabel) {
            pageInfoLabel.textContent = 'A4 (210×297mm)';
        }
    }

    /**
     * 设置指示器样式
     * @private
     */
    _setupIndicatorStyles() {
        if (!this.indicator) return;

        // 确保指示器具有正确的样式类
        this.indicator.classList.add('print-range-indicator');
        
        // 设置初始隐藏状态
        this.indicator.classList.add('hidden');
    }

    /**
     * 获取导出打印设置
     * @param {string} documentType - 文档类型
     * @returns {Object} 打印设置
     */
    getExportPrintSettings(documentType) {
        // 默认A4打印设置
        const defaultSettings = {
            paperSize: 'A4',
            orientation: 'portrait',
            margins: { top: 20, bottom: 15, left: 10, right: 10 },
            scale: 1.0
        };

        // 根据文档类型调整设置
        const typeSpecificSettings = {
            'receipt': {
                margins: { top: 20, bottom: 15, left: 10, right: 10 },
                scale: 0.9
            },
            'invoice': {
                margins: { top: 25, bottom: 20, left: 15, right: 15 },
                scale: 1.0
            },
            'quotation': {
                margins: { top: 25, bottom: 20, left: 15, right: 15 },
                scale: 1.0
            },
            'driver_agreement': {
                margins: { top: 15, bottom: 10, left: 10, right: 10 },
                scale: 0.8
            }
        };

        return {
            ...defaultSettings,
            ...(typeSpecificSettings[documentType] || {})
        };
    }
}
// #endregion

// #region A4预览缩放管理器
/**
 * @object A4PreviewZoomManager - A4预览缩放管理器
 * @description 管理文档预览的缩放功能
 */
export const A4PreviewZoomManager = {
    currentZoom: 0.75, // 默认75%缩放
    minZoom: 0.3,      // 最小30%
    maxZoom: 2.0,      // 最大200%
    zoomStep: 0.1,     // 缩放步长10%
    
    /**
     * 初始化缩放管理器
     */
    initialize() {
        try {
            pageLogger.info('A4PreviewZoomManager', 'initialize', '🔍 初始化A4预览缩放管理器');
            
            // 获取缩放控制元素
            this.zoomInBtn = document.getElementById('zoom-in-button');
            this.zoomOutBtn = document.getElementById('zoom-out-button');
            this.zoomResetBtn = document.getElementById('zoom-reset-button');
            this.zoomLevelDisplay = document.getElementById('zoom-level');
            this.documentContainer = document.getElementById('document-container');
            
            // 设置事件监听器
            this._setupEventListeners();
            
            // 应用初始缩放
            this.applyZoom(this.currentZoom);
            
            pageLogger.info('A4PreviewZoomManager', 'initialize', '✅ A4预览缩放管理器初始化完成');
            
        } catch (error) {
            pageLogger.error('A4PreviewZoomManager', 'initialize', '❌ A4预览缩放管理器初始化失败', error);
        }
    },

    /**
     * 设置事件监听器
     * @private
     */
    _setupEventListeners() {
        // 放大按钮
        if (this.zoomInBtn) {
            this.zoomInBtn.addEventListener('click', () => {
                this.zoomIn();
            });
        }

        // 缩小按钮
        if (this.zoomOutBtn) {
            this.zoomOutBtn.addEventListener('click', () => {
                this.zoomOut();
            });
        }

        // 重置按钮
        if (this.zoomResetBtn) {
            this.zoomResetBtn.addEventListener('click', () => {
                this.resetZoom();
            });
        }

        // 鼠标滚轮缩放（Ctrl + 滚轮）
        if (this.documentContainer) {
            this.documentContainer.addEventListener('wheel', (e) => {
                if (e.ctrlKey) {
                    e.preventDefault();
                    if (e.deltaY < 0) {
                        this.zoomIn();
                    } else {
                        this.zoomOut();
                    }
                }
            });
        }
    },

    /**
     * 放大
     */
    zoomIn() {
        const newZoom = Math.min(this.currentZoom + this.zoomStep, this.maxZoom);
        this.applyZoom(newZoom);
    },

    /**
     * 缩小
     */
    zoomOut() {
        const newZoom = Math.max(this.currentZoom - this.zoomStep, this.minZoom);
        this.applyZoom(newZoom);
    },

    /**
     * 重置缩放
     */
    resetZoom() {
        this.applyZoom(0.75); // 重置到75%
    },

    /**
     * 应用缩放
     * @param {number} zoom - 缩放比例
     */
    applyZoom(zoom) {
        if (!this.documentContainer) return;
        
        this.currentZoom = Math.max(this.minZoom, Math.min(zoom, this.maxZoom));
        
        // 应用CSS变换
        this.documentContainer.style.transform = `scale(${this.currentZoom})`;
        this.documentContainer.style.transformOrigin = 'top left';
        
        // 更新显示
        this._updateZoomDisplay();
        
        // 更新按钮状态
        this._updateButtonStates();
        
        pageLogger.debug('A4PreviewZoomManager', 'applyZoom', `缩放已应用: ${(this.currentZoom * 100).toFixed(0)}%`);
    },

    /**
     * 更新缩放显示
     * @private
     */
    _updateZoomDisplay() {
        if (this.zoomLevelDisplay) {
            this.zoomLevelDisplay.textContent = `${(this.currentZoom * 100).toFixed(0)}%`;
        }
    },

    /**
     * 更新按钮状态
     * @private
     */
    _updateButtonStates() {
        // 更新放大按钮状态
        if (this.zoomInBtn) {
            this.zoomInBtn.disabled = this.currentZoom >= this.maxZoom;
        }

        // 更新缩小按钮状态
        if (this.zoomOutBtn) {
            this.zoomOutBtn.disabled = this.currentZoom <= this.minZoom;
        }
    },

    /**
     * 获取当前缩放比例
     * @returns {number} 当前缩放比例
     */
    getCurrentZoom() {
        return this.currentZoom;
    },

    /**
     * 设置缩放比例
     * @param {number} zoom - 缩放比例
     */
    setZoom(zoom) {
        this.applyZoom(zoom);
    }
};
// #endregion

// #region 导出函数
/**
 * 创建打印范围指示器管理器
 * @returns {PrintRangeIndicatorManager} 管理器实例
 */
export function createPrintRangeIndicatorManager() {
    return new PrintRangeIndicatorManager();
}

/**
 * 获取全局打印范围指示器管理器
 * @returns {PrintRangeIndicatorManager} 全局管理器实例
 */
export function getPrintRangeIndicatorManager() {
    if (!window._globalPrintRangeManager) {
        window._globalPrintRangeManager = new PrintRangeIndicatorManager();
    }
    return window._globalPrintRangeManager;
}

/**
 * 获取A4预览缩放管理器
 * @returns {Object} A4预览缩放管理器
 */
export function getA4PreviewZoomManager() {
    return A4PreviewZoomManager;
}

/**
 * 初始化预览管理器
 */
export function initializePreviewManagers() {
    // 初始化打印范围指示器管理器
    getPrintRangeIndicatorManager();
    
    // 初始化A4预览缩放管理器
    A4PreviewZoomManager.initialize();
    
    pageLogger.info('PreviewManager', 'initializePreviewManagers', '✅ 预览管理器初始化完成');
}
// #endregion
