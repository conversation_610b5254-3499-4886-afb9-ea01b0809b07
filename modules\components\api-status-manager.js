/**
 * @file API状态管理器 - 管理API连接状态和配置
 * 负责Gemini API连接测试、状态显示和配置管理
 */

(function() {
    'use strict';

    /**
     * API状态管理器类
     * @class ApiStatusManager
     */
    class ApiStatusManager {
        constructor() {
            this.statusIndicator = null;
            this.statusText = null;
            this.currentStatus = 'checking';
            this.apiKey = 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s'; // 硬植入的API密钥
            this.testInProgress = false;
            
            this.init();
        }

        /**
         * 初始化API状态管理器
         * @function init
         */
        init() {
            this.statusIndicator = document.getElementById('api-status-indicator');
            this.statusText = this.statusIndicator?.querySelector('.api-status-text');
            
            if (this.statusIndicator) {
                this.updateApiStatus('checking');
                // 延迟测试API连接
                setTimeout(() => this.testGeminiConnection(), 1000);
            }
        }

        /**
         * 更新API状态显示
         * @function updateApiStatus
         * @param {string} status - 状态：'checking', 'connected', 'error', 'offline'
         * @param {string} message - 自定义状态消息
         */
        updateApiStatus(status, message = null) {
            if (!this.statusIndicator || !this.statusText) return;

            this.currentStatus = status;
            
            const statusConfig = {
                checking: {
                    bgClass: 'bg-gray-700',
                    textClass: 'text-gray-300',
                    iconClass: 'bg-gray-400',
                    pulseClass: 'bg-gray-400',
                    message: message || 'API状态检查中...'
                },
                connected: {
                    bgClass: 'bg-green-700',
                    textClass: 'text-green-100',
                    iconClass: 'bg-green-400',
                    pulseClass: 'bg-green-400',
                    message: message || 'Gemini API已连接'
                },
                error: {
                    bgClass: 'bg-red-700',
                    textClass: 'text-red-100',
                    iconClass: 'bg-red-400',
                    pulseClass: 'bg-red-400',
                    message: message || 'API连接失败'
                },
                offline: {
                    bgClass: 'bg-yellow-700',
                    textClass: 'text-yellow-100',
                    iconClass: 'bg-yellow-400',
                    pulseClass: 'bg-yellow-400',
                    message: message || '离线模式'
                }
            };

            const config = statusConfig[status] || statusConfig.checking;

            // 更新状态指示器样式
            this.statusIndicator.className = `text-sm ${config.bgClass} ${config.textClass} px-3 py-1 rounded-full flex items-center`;
            
            // 更新状态图标
            const iconContainer = this.statusIndicator.querySelector('.flex.h-2.w-2');
            if (iconContainer) {
                const pulseIcon = iconContainer.querySelector('.animate-ping');
                const staticIcon = iconContainer.querySelector('.relative');
                
                if (pulseIcon) {
                    pulseIcon.className = `animate-ping absolute inline-flex h-full w-full rounded-full ${config.pulseClass} opacity-75`;
                }
                if (staticIcon) {
                    staticIcon.className = `relative inline-flex rounded-full h-2 w-2 ${config.iconClass}`;
                }
            }

            // 更新状态文本
            this.statusText.textContent = config.message;

            // 记录日志
            if (window.SmartOffice && window.SmartOffice.Logger) {
                window.SmartOffice.Logger.debug('ApiStatusManager', 'updateApiStatus', `API状态更新为: ${status}`, {
                    status, message: config.message
                });
            }
        }

        /**
         * 测试Gemini API连接
         * @function testGeminiConnection
         * @returns {Promise<boolean>} 连接测试结果
         */
        async testGeminiConnection() {
            if (this.testInProgress) return false;
            
            this.testInProgress = true;
            this.updateApiStatus('checking', 'Gemini API连接测试中...');

            try {
                // 获取NLP管理器
                const nlpManager = this._getNLPManager();
                if (!nlpManager) {
                    throw new Error('NLP管理器未初始化');
                }

                // 执行简单的连接测试
                const testText = '测试连接：张三预订了1晚酒店，价格100元';
                const testResult = await nlpManager.processText(testText);

                if (testResult && testResult.confidence > 0) {
                    this.updateApiStatus('connected');
                    
                    // 显示成功通知
                    if (window.showSuccessNotification) {
                        window.showSuccessNotification('连接成功', 'Gemini API连接正常，可以正常使用');
                    }

                    // 记录成功日志
                    if (window.SmartOffice && window.SmartOffice.Logger) {
                        window.SmartOffice.Logger.info('ApiStatusManager', 'testGeminiConnection', '✅ Gemini API连接测试成功', {
                            confidence: testResult.confidence,
                            extractedFields: Object.keys(testResult.extractedData || {}).length
                        });
                    }

                    return true;
                } else {
                    throw new Error('API响应无效');
                }
                
            } catch (error) {
                this.updateApiStatus('error', `连接失败: ${error.message}`);
                
                // 显示错误通知
                if (window.showErrorNotification) {
                    window.showErrorNotification('连接失败', `Gemini API连接失败：${error.message}`);
                }

                // 记录错误日志
                if (window.SmartOffice && window.SmartOffice.Logger) {
                    window.SmartOffice.Logger.error('ApiStatusManager', 'testGeminiConnection', '❌ Gemini API连接测试失败', {
                        error: error.message
                    });
                }

                return false;
            } finally {
                this.testInProgress = false;
            }
        }

        /**
         * 更新NLP配置
         * @function updateNLPConfig
         * @param {Object} config - NLP配置
         */
        updateNLPConfig(config = {}) {
            const mode = document.getElementById('nlp-mode-selector')?.value || 'gemini';
            
            const nlpConfig = {
                mode: mode,
                apiKey: this.apiKey,
                enableImageProcessing: true,
                enableMultiModal: true,
                ...config
            };
            
            // 更新应用配置
            if (window.smartOfficeApp && window.smartOfficeApp.getManager) {
                const configManager = window.smartOfficeApp.getManager('offlineConfig');
                if (configManager) {
                    configManager.set('nlp', nlpConfig);
                }
            }
            
            // 设置NLP管理器的API密钥
            const nlpManager = this._getNLPManager();
            if (nlpManager && typeof nlpManager.setApiKey === 'function') {
                try {
                    nlpManager.setApiKey(this.apiKey);
                    
                    if (window.SmartOffice && window.SmartOffice.Logger) {
                        window.SmartOffice.Logger.info('ApiStatusManager', 'updateNLPConfig', '✅ NLP管理器API密钥已设置', { 
                            apiKeyLength: this.apiKey.length 
                        });
                    }
                } catch (error) {
                    if (window.SmartOffice && window.SmartOffice.Logger) {
                        window.SmartOffice.Logger.error('ApiStatusManager', 'updateNLPConfig', '❌ NLP管理器API密钥设置失败', { 
                            error: error.message 
                        });
                    }
                }
            }
            
            // 存储到本地存储
            try {
                localStorage.setItem('smartoffice_nlp_config', JSON.stringify(nlpConfig));
            } catch (error) {
                if (window.SmartOffice && window.SmartOffice.Logger) {
                    window.SmartOffice.Logger.warn('ApiStatusManager', 'updateNLPConfig', '配置保存到本地存储失败', { 
                        error: error.message 
                    });
                }
            }

            // 记录配置更新
            if (window.SmartOffice && window.SmartOffice.Logger) {
                window.SmartOffice.Logger.debug('ApiStatusManager', 'updateNLPConfig', 'NLP配置已更新（硬植入模式）', nlpConfig);
            }
        }

        /**
         * 获取NLP管理器
         * @function _getNLPManager
         * @returns {Object|null} NLP管理器实例
         * @private
         */
        _getNLPManager() {
            if (window.smartOfficeApp && window.smartOfficeApp.getManager) {
                return window.smartOfficeApp.getManager('nlp');
            }
            return null;
        }

        /**
         * 获取当前API状态
         * @function getStatus
         * @returns {string} 当前状态
         */
        getStatus() {
            return this.currentStatus;
        }

        /**
         * 手动重新测试连接
         * @function retestConnection
         */
        async retestConnection() {
            await this.testGeminiConnection();
        }

        /**
         * 清理资源
         * @function cleanup
         */
        cleanup() {
            this.testInProgress = false;
            this.statusIndicator = null;
            this.statusText = null;
        }
    }

    // 导出到全局命名空间
    if (!window.SmartOffice) {
        window.SmartOffice = {};
    }
    if (!window.SmartOffice.Components) {
        window.SmartOffice.Components = {};
    }

    window.SmartOffice.Components.ApiStatusManager = ApiStatusManager;

    // 兼容性：导出全局函数
    window.updateApiStatus = function(status, message) {
        if (window.SmartOffice.Components.apiStatusManager) {
            window.SmartOffice.Components.apiStatusManager.updateApiStatus(status, message);
        }
    };

    window.testGeminiConnection = function() {
        if (window.SmartOffice.Components.apiStatusManager) {
            return window.SmartOffice.Components.apiStatusManager.testGeminiConnection();
        }
    };

    window.updateNLPConfig = function(config) {
        if (window.SmartOffice.Components.apiStatusManager) {
            window.SmartOffice.Components.apiStatusManager.updateNLPConfig(config);
        }
    };

})();
