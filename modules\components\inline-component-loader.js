/**
 * @file 内联组件加载器 - 传统script tag架构的组件管理
 * 负责加载和初始化所有内联组件，保持file://协议兼容性
 */

(function() {
    'use strict';

    /**
     * 内联组件加载器类
     * @class InlineComponentLoader
     */
    class InlineComponentLoader {
        constructor() {
            this.loadedComponents = new Set();
            this.componentInstances = new Map();
            this.initialized = false;
            this.logger = null;
        }

        /**
         * 初始化组件加载器
         * @function init
         */
        async init() {
            if (this.initialized) return;

            this.logger = window.SmartOffice?.Logger || console;
            this.logger.info('InlineComponentLoader', 'init', '🔄 初始化内联组件加载器');

            // 等待DOM加载完成
            if (document.readyState === 'loading') {
                await new Promise(resolve => {
                    document.addEventListener('DOMContentLoaded', resolve);
                });
            }

            // 加载所有组件
            await this.loadAllComponents();

            this.initialized = true;
            this.logger.info('InlineComponentLoader', 'init', '✅ 内联组件加载器初始化完成');
        }

        /**
         * 加载所有组件
         * @function loadAllComponents
         */
        async loadAllComponents() {
            this.logger.info('InlineComponentLoader', 'loadAllComponents', '🔄 开始加载所有内联组件');

            // 按依赖顺序加载组件
            const loadOrder = [
                'notification-manager',
                'api-status-manager', 
                'form-data-manager',
                'app-initializer-manager',
                'resource-manager'
            ];

            for (const componentName of loadOrder) {
                await this.loadComponent(componentName);
            }

            // 初始化组件实例
            await this.initializeComponentInstances();

            this.logger.info('InlineComponentLoader', 'loadAllComponents', '✅ 所有内联组件加载完成');
        }

        /**
         * 加载单个组件
         * @function loadComponent
         * @param {string} componentName - 组件名称
         */
        async loadComponent(componentName) {
            if (this.loadedComponents.has(componentName)) {
                return;
            }

            try {
                this.logger.debug('InlineComponentLoader', 'loadComponent', `🔄 加载组件: ${componentName}`);

                // 检查组件是否已经通过script标签加载
                const isLoaded = this.checkComponentLoaded(componentName);
                
                if (isLoaded) {
                    this.loadedComponents.add(componentName);
                    this.logger.debug('InlineComponentLoader', 'loadComponent', `✅ 组件已加载: ${componentName}`);
                } else {
                    this.logger.warn('InlineComponentLoader', 'loadComponent', `⚠️ 组件未找到: ${componentName}`);
                }

            } catch (error) {
                this.logger.error('InlineComponentLoader', 'loadComponent', `❌ 加载组件失败: ${componentName}`, {
                    error: error.message
                });
            }
        }

        /**
         * 检查组件是否已加载
         * @function checkComponentLoaded
         * @param {string} componentName - 组件名称
         * @returns {boolean} 是否已加载
         */
        checkComponentLoaded(componentName) {
            const componentMap = {
                'notification-manager': () => window.SmartOffice?.Components?.NotificationManager,
                'api-status-manager': () => window.SmartOffice?.Components?.ApiStatusManager,
                'form-data-manager': () => window.SmartOffice?.Components?.FormDataManager,
                'app-initializer-manager': () => window.SmartOffice?.Components?.AppInitializerManager,
                'resource-manager': () => window.SmartOffice?.Utils?.InlineResourceManager
            };

            const checker = componentMap[componentName];
            return checker ? !!checker() : false;
        }

        /**
         * 初始化组件实例
         * @function initializeComponentInstances
         */
        async initializeComponentInstances() {
            this.logger.debug('InlineComponentLoader', 'initializeComponentInstances', '🔄 初始化组件实例');

            // 初始化通知管理器
            if (window.SmartOffice?.Components?.NotificationManager && !window.SmartOffice.Components.notificationManager) {
                const notificationManager = new window.SmartOffice.Components.NotificationManager();
                window.SmartOffice.Components.notificationManager = notificationManager;
                this.componentInstances.set('notificationManager', notificationManager);
                this.logger.debug('InlineComponentLoader', 'initializeComponentInstances', '✅ 通知管理器实例已创建');
            }

            // 初始化API状态管理器
            if (window.SmartOffice?.Components?.ApiStatusManager && !window.SmartOffice.Components.apiStatusManager) {
                const apiStatusManager = new window.SmartOffice.Components.ApiStatusManager();
                window.SmartOffice.Components.apiStatusManager = apiStatusManager;
                this.componentInstances.set('apiStatusManager', apiStatusManager);
                this.logger.debug('InlineComponentLoader', 'initializeComponentInstances', '✅ API状态管理器实例已创建');
            }

            // 初始化表单数据管理器
            if (window.SmartOffice?.Components?.FormDataManager && !window.SmartOffice.Components.formDataManager) {
                const formDataManager = new window.SmartOffice.Components.FormDataManager();
                window.SmartOffice.Components.formDataManager = formDataManager;
                this.componentInstances.set('formDataManager', formDataManager);
                this.logger.debug('InlineComponentLoader', 'initializeComponentInstances', '✅ 表单数据管理器实例已创建');
            }

            // 初始化资源管理器
            if (window.SmartOffice?.Utils?.InlineResourceManager && !window.SmartOffice.Utils.inlineResourceManager) {
                const resourceManager = new window.SmartOffice.Utils.InlineResourceManager();
                await resourceManager.init();
                window.SmartOffice.Utils.inlineResourceManager = resourceManager;
                this.componentInstances.set('resourceManager', resourceManager);
                this.logger.debug('InlineComponentLoader', 'initializeComponentInstances', '✅ 资源管理器实例已创建');
            }

            // 初始化应用初始化管理器
            if (window.SmartOffice?.Components?.AppInitializerManager && !window.SmartOffice.Components.appInitializerManager) {
                const appInitializerManager = new window.SmartOffice.Components.AppInitializerManager();
                window.SmartOffice.Components.appInitializerManager = appInitializerManager;
                this.componentInstances.set('appInitializerManager', appInitializerManager);
                this.logger.debug('InlineComponentLoader', 'initializeComponentInstances', '✅ 应用初始化管理器实例已创建');
            }
        }

        /**
         * 获取组件实例
         * @function getComponentInstance
         * @param {string} name - 组件名称
         * @returns {Object|null} 组件实例
         */
        getComponentInstance(name) {
            return this.componentInstances.get(name) || null;
        }

        /**
         * 获取所有组件实例
         * @function getAllComponentInstances
         * @returns {Map} 所有组件实例
         */
        getAllComponentInstances() {
            return new Map(this.componentInstances);
        }

        /**
         * 检查组件是否已初始化
         * @function isComponentInitialized
         * @param {string} name - 组件名称
         * @returns {boolean} 是否已初始化
         */
        isComponentInitialized(name) {
            return this.componentInstances.has(name);
        }

        /**
         * 获取加载状态
         * @function getLoadStatus
         * @returns {Object} 加载状态信息
         */
        getLoadStatus() {
            return {
                initialized: this.initialized,
                loadedComponents: Array.from(this.loadedComponents),
                componentInstances: Array.from(this.componentInstances.keys()),
                totalLoaded: this.loadedComponents.size,
                totalInstances: this.componentInstances.size
            };
        }

        /**
         * 清理资源
         * @function cleanup
         */
        cleanup() {
            this.logger.info('InlineComponentLoader', 'cleanup', '🧹 开始清理组件加载器资源');

            // 清理组件实例
            for (const [name, instance] of this.componentInstances) {
                if (instance && typeof instance.cleanup === 'function') {
                    try {
                        instance.cleanup();
                        this.logger.debug('InlineComponentLoader', 'cleanup', `组件实例 ${name} 已清理`);
                    } catch (error) {
                        this.logger.error('InlineComponentLoader', 'cleanup', `清理组件实例 ${name} 失败`, {
                            error: error.message
                        });
                    }
                }
            }

            this.loadedComponents.clear();
            this.componentInstances.clear();
            this.initialized = false;

            this.logger.info('InlineComponentLoader', 'cleanup', '✅ 组件加载器资源清理完成');
        }
    }

    // 创建全局实例
    const inlineComponentLoader = new InlineComponentLoader();

    // 导出到全局命名空间
    if (!window.SmartOffice) {
        window.SmartOffice = {};
    }
    if (!window.SmartOffice.Components) {
        window.SmartOffice.Components = {};
    }

    window.SmartOffice.Components.InlineComponentLoader = InlineComponentLoader;
    window.SmartOffice.Components.inlineComponentLoader = inlineComponentLoader;

    // 兼容性：导出全局函数
    window.initializeInlineComponents = async function() {
        await inlineComponentLoader.init();
        return inlineComponentLoader;
    };

    window.getInlineComponentLoader = function() {
        return inlineComponentLoader;
    };

})();
