/**
 * @file 导出管理器模块 - 从index.html提取的导出功能
 * @description 提供文档导出为图片和PDF的功能
 */

import { getLogger } from '../core/logger.js';
import { showSuccessNotification, showErrorNotification } from './event-handlers.js';

// #region 全局变量
const pageLogger = getLogger();
// #endregion

// #region 导出管理器类
/**
 * @class ExportManager - 导出管理器
 * @description 管理文档的图片和PDF导出功能
 */
export class ExportManager {
    /**
     * 构造函数 - 初始化导出管理器
     */
    constructor() {
        this.isExporting = false;
        this.exportQueue = [];
        
        pageLogger.info('ExportManager', 'constructor', '📤 导出管理器已创建');
        this._initializeExportButtons();
    }

    /**
     * 导出为图片
     * @param {HTMLElement} element - 要导出的元素
     * @param {Object} options - 导出选项
     * @returns {Promise<void>}
     */
    async exportAsImage(element = null, options = {}) {
        if (this.isExporting) {
            pageLogger.warn('ExportManager', 'exportAsImage', '导出正在进行中，请稍候');
            return;
        }

        this.isExporting = true;
        
        try {
            const documentContainer = element || document.getElementById('document-container');
            if (!documentContainer) {
                throw new Error('找不到要导出的文档容器');
            }

            pageLogger.info('ExportManager', 'exportAsImage', '🖼️ 开始导出图片');

            // 检查html2canvas是否可用
            if (typeof html2canvas === 'undefined') {
                throw new Error('html2canvas库未加载，无法导出图片');
            }

            // 保存原始样式
            const originalTransform = documentContainer.style.transform;
            const originalZoom = documentContainer.style.zoom;

            // 临时移除缩放变换以确保导出质量
            documentContainer.style.transform = 'none';
            documentContainer.style.zoom = '1';

            // 配置导出选项
            const exportOptions = {
                scale: 2, // 高分辨率
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: documentContainer.scrollWidth,
                height: documentContainer.scrollHeight,
                ...options
            };

            // 执行导出
            const canvas = await html2canvas(documentContainer, exportOptions);
            
            // 恢复原始样式
            documentContainer.style.transform = originalTransform;
            documentContainer.style.zoom = originalZoom;

            // 生成文件名
            const docType = this._getCurrentDocumentType() || 'document';
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
            const filename = `${docType}_${timestamp}.png`;
            
            // 创建下载链接并自动下载
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            pageLogger.info('ExportManager', 'exportAsImage', '✅ 图片导出成功', { filename });
            showSuccessNotification('导出成功', `图片已保存为 ${filename}`);

        } catch (error) {
            pageLogger.error('ExportManager', 'exportAsImage', '❌ 图片导出失败', error);
            showErrorNotification('导出失败', `图片导出失败: ${error.message}`);
            throw error;
        } finally {
            this.isExporting = false;
        }
    }

    /**
     * 导出为PDF
     * @param {HTMLElement} element - 要导出的元素
     * @param {Object} options - 导出选项
     * @returns {Promise<void>}
     */
    async exportAsPDF(element = null, options = {}) {
        if (this.isExporting) {
            pageLogger.warn('ExportManager', 'exportAsPDF', '导出正在进行中，请稍候');
            return;
        }

        this.isExporting = true;
        
        try {
            const documentContainer = element || document.getElementById('document-container');
            if (!documentContainer) {
                throw new Error('找不到要导出的文档容器');
            }

            pageLogger.info('ExportManager', 'exportAsPDF', '📄 开始导出PDF');

            // 检查依赖库
            if (typeof html2canvas === 'undefined') {
                throw new Error('html2canvas库未加载，无法导出PDF');
            }
            if (typeof window.jspdf === 'undefined' || !window.jspdf.jsPDF) {
                throw new Error('jsPDF库未加载，无法导出PDF');
            }

            // 保存原始样式
            const originalTransform = documentContainer.style.transform;
            const originalZoom = documentContainer.style.zoom;

            // 临时移除缩放变换
            documentContainer.style.transform = 'none';
            documentContainer.style.zoom = '1';

            // 配置导出选项
            const exportOptions = {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                width: documentContainer.scrollWidth,
                height: documentContainer.scrollHeight,
                ...options
            };

            // 生成canvas
            const canvas = await html2canvas(documentContainer, exportOptions);
            
            // 恢复原始样式
            documentContainer.style.transform = originalTransform;
            documentContainer.style.zoom = originalZoom;

            // 创建PDF
            const { jsPDF } = window.jspdf;
            const pdf = new jsPDF('p', 'mm', 'a4');
            
            // 计算图片尺寸
            const imgWidth = 210; // A4宽度
            const imgHeight = (canvas.height * imgWidth) / canvas.width;
            
            // 添加图片到PDF
            const imgData = canvas.toDataURL('image/png');
            pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);

            // 生成文件名
            const docType = this._getCurrentDocumentType() || 'document';
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
            const filename = `${docType}_${timestamp}.pdf`;
            
            // 保存PDF文件
            pdf.save(filename);

            pageLogger.info('ExportManager', 'exportAsPDF', '✅ PDF导出成功', { filename });
            showSuccessNotification('导出成功', `PDF已保存为 ${filename}`);

        } catch (error) {
            pageLogger.error('ExportManager', 'exportAsPDF', '❌ PDF导出失败', error);
            showErrorNotification('导出失败', `PDF导出失败: ${error.message}`);
            throw error;
        } finally {
            this.isExporting = false;
        }
    }

    /**
     * 批量导出
     * @param {Array} elements - 要导出的元素数组
     * @param {string} format - 导出格式 ('image' | 'pdf')
     * @param {Object} options - 导出选项
     * @returns {Promise<void>}
     */
    async batchExport(elements, format = 'image', options = {}) {
        if (this.isExporting) {
            pageLogger.warn('ExportManager', 'batchExport', '导出正在进行中，请稍候');
            return;
        }

        try {
            pageLogger.info('ExportManager', 'batchExport', `📦 开始批量导出 ${format}`, {
                count: elements.length
            });

            const results = [];
            
            for (let i = 0; i < elements.length; i++) {
                const element = elements[i];
                
                try {
                    if (format === 'image') {
                        await this.exportAsImage(element, options);
                    } else if (format === 'pdf') {
                        await this.exportAsPDF(element, options);
                    }
                    
                    results.push({ index: i, success: true });
                    
                    // 添加延迟避免过快导出
                    if (i < elements.length - 1) {
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                    
                } catch (error) {
                    pageLogger.error('ExportManager', 'batchExport', `第${i+1}个元素导出失败`, error);
                    results.push({ index: i, success: false, error: error.message });
                }
            }

            const successCount = results.filter(r => r.success).length;
            pageLogger.info('ExportManager', 'batchExport', '✅ 批量导出完成', {
                total: elements.length,
                success: successCount,
                failed: elements.length - successCount
            });

            showSuccessNotification('批量导出完成', `成功导出 ${successCount}/${elements.length} 个文档`);

        } catch (error) {
            pageLogger.error('ExportManager', 'batchExport', '❌ 批量导出失败', error);
            showErrorNotification('批量导出失败', error.message);
            throw error;
        }
    }

    // #region 私有方法
    /**
     * 初始化导出按钮事件
     * @private
     */
    _initializeExportButtons() {
        // 图片导出按钮
        const exportImageBtn = document.getElementById('export-image-button');
        if (exportImageBtn) {
            exportImageBtn.addEventListener('click', () => {
                this.exportAsImage();
            });
        }

        // PDF导出按钮
        const exportPdfBtn = document.getElementById('export-pdf-button');
        if (exportPdfBtn) {
            exportPdfBtn.addEventListener('click', () => {
                this.exportAsPDF();
            });
        }

        // 移动端导出按钮
        const mobileExportBtn = document.getElementById('mobile-export');
        if (mobileExportBtn) {
            mobileExportBtn.addEventListener('click', () => {
                this.exportAsImage();
            });
        }

        const mobilePdfBtn = document.getElementById('mobile-pdf-export');
        if (mobilePdfBtn) {
            mobilePdfBtn.addEventListener('click', () => {
                this.exportAsPDF();
            });
        }

        pageLogger.debug('ExportManager', '_initializeExportButtons', '导出按钮事件已初始化');
    }

    /**
     * 获取当前文档类型
     * @returns {string} 文档类型
     * @private
     */
    _getCurrentDocumentType() {
        const documentTypeSelect = document.getElementById('document-type');
        if (documentTypeSelect) {
            const selectedValue = documentTypeSelect.value;
            const typeMap = {
                'receipt': '收据',
                'invoice': '发票', 
                'quotation': '报价单',
                'driver_agreement': '司机协议'
            };
            return typeMap[selectedValue] || selectedValue;
        }
        return 'document';
    }
    // #endregion
}
// #endregion

// #region 导出函数
/**
 * 创建导出管理器实例
 * @returns {ExportManager} 导出管理器实例
 */
export function createExportManager() {
    return new ExportManager();
}

/**
 * 获取全局导出管理器
 * @returns {ExportManager} 全局导出管理器实例
 */
export function getExportManager() {
    if (!window._globalExportManager) {
        window._globalExportManager = new ExportManager();
    }
    return window._globalExportManager;
}

// 便捷导出函数
export async function exportAsImage(element, options) {
    const manager = getExportManager();
    return await manager.exportAsImage(element, options);
}

export async function exportAsPDF(element, options) {
    const manager = getExportManager();
    return await manager.exportAsPDF(element, options);
}
// #endregion
