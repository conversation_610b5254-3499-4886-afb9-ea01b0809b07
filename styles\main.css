/**
 * @file SmartOffice 2.0 主样式文件
 * @description 整合后的统一样式文件，消除重复定义，优化性能
 * @version 3.0.0 - 第三阶段重构版本
 * <AUTHOR> Team
 * 
 * 重构说明：
 * - 整合了8个分散的CSS文件
 * - 消除了90%以上的重复CSS规则
 * - 统一了CSS变量命名规范
 * - 建立了清晰的样式层级结构
 * - 优化了选择器性能
 */

/* #region CSS变量定义 - 统一的全局变量系统 */
:root {
    /* A4纸张标准尺寸 (毫米转换为像素，96 DPI) */
    --a4-width-px: 794px;
    --a4-height-px: 1123px;
    
    /* 布局变量 - 统一页眉页脚高度定义 */
    --header-height: 160px;
    --footer-height: 38px;
    --content-padding: 20px;
    --document-max-width: 595px;
    --document-min-height: 842px;
    
    /* 页边距 (毫米转换为像素) */
    --margin-top-px: 170px;
    --margin-bottom-px: 60px;
    --margin-left-px: 37.8px;
    --margin-right-px: 37.8px;
    
    /* 内容区域尺寸 */
    --content-width-px: calc(var(--a4-width-px) - var(--margin-left-px) - var(--margin-right-px));
    --content-height-px: calc(var(--a4-height-px) - var(--margin-top-px) - var(--margin-bottom-px));
    
    /* 颜色变量 - 主题色彩系统 */
    --primary-color: #1e40af;
    --secondary-color: #3b82f6;
    --accent-color: #f59e0b;
    --light-color: #f3f4f6;
    --dark-color: #1f2937;
    --background-color: #f9fafb;
    --text-color: #333333;
    --border-color: #e5e7eb;
    
    /* 字体变量 */
    --base-font-family: 'Roboto', 'Noto Sans SC', sans-serif;
    --classic-font-family: 'Times New Roman', 'SimSun', serif;
    --elegant-font-family: 'Georgia', 'STZhongsong', serif;
    --tourism-font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
    --base-font-size: 11pt;
    --title-font-size: 18pt;
    --small-font-size: 9pt;
    --line-height: 1.5;
    
    /* 阴影变量 */
    --box-shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --box-shadow-medium: 0 4px 15px -1px rgba(0, 0, 0, 0.1), 0 6px 8px -1px rgba(0, 0, 0, 0.05);
    --box-shadow-heavy: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    
    /* 过渡变量 */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;
    
    /* Z-index层级 */
    --z-index-header: 100;
    --z-index-footer: 100;
    --z-index-stamp: 10000;
    --z-index-modal: 1000;
    --z-index-tooltip: 1100;
    
    /* 预览容器缩放比例 */
    --preview-scale-factor: 0.8;
    --preview-scale-min: 0.45;
    --preview-scale-max: 0.8;
    
    /* 印章定位 */
    --stamp-bottom-offset: 20%;
    --stamp-right-offset: 8%;
}
/* #endregion */

/* #region 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--base-font-family);
    font-size: var(--base-font-size);
    line-height: var(--line-height);
    color: var(--text-color);
    background-color: var(--background-color);
    -ms-overflow-style: none;
    overflow: -moz-scrollbars-auto;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--light-color);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dark-color);
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

.hidden-field {
    display: none !important;
}
/* #endregion */

/* #region A4纸张和文档容器样式 */
.a4-page {
    width: var(--a4-width-px);
    min-height: var(--a4-height-px);
    background: white;
    margin: 0 auto;
    box-shadow: var(--box-shadow-light);
    position: relative;
    overflow: hidden;
}

#document-preview {
    width: 100%;
    max-width: var(--a4-width-px);
    min-height: var(--a4-height-px);
    height: auto;
    margin: 0 auto 30px;
    padding: 0;
    background-color: white;
    box-shadow: var(--box-shadow-medium);
    transform: scale(var(--preview-scale-factor));
    transform-origin: top center;
    transition: var(--transition-normal);
    position: relative;
    overflow: visible;
    aspect-ratio: 1 / 1.414;
}

#document-container {
    position: relative;
    width: var(--a4-width-px);
    min-height: var(--a4-height-px);
    background: white;
    margin: 0 auto;
    padding-top: var(--header-height);
    padding-bottom: var(--footer-height);
    padding-left: var(--content-padding);
    padding-right: var(--content-padding);
    display: flex;
    flex-direction: column;
    font-size: var(--base-font-size);
    line-height: var(--line-height);
    color: var(--text-color);
    overflow: visible;
    transform-origin: top center;
}

#preview-container {
    background: #f5f5f5;
    padding: 20px;
    min-height: 100vh;
    overflow: auto;
    height: 600px;
}

/* 打印范围指示器 */
.print-range-indicator {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 10;
}
/* #endregion */

/* #region 页眉页脚样式 - 统一的定位和样式 */
.document-header,
.document-header-image-container {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    z-index: var(--z-index-header);
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--box-shadow-light);
    padding: 5px;
}

.document-header-image-container img {
    max-height: calc(var(--header-height) - 10px);
    max-width: 100%;
    object-fit: contain;
    display: block;
}

.document-footer,
.document-footer-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: var(--footer-height);
    z-index: var(--z-index-footer);
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    border-top: 1px solid var(--border-color);
}

.document-footer-content {
    width: 100%;
    text-align: center;
    font-size: 8pt;
    color: #666;
    line-height: 1.2;
    padding: 2px 5px;
}

/* 统一页脚样式 */
.unified-document-footer.company-footer-image-container {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: auto;
    min-height: var(--footer-height);
    background-color: white;
    z-index: var(--z-index-footer);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    padding: 5px 5px 2px 5px;
    box-shadow: 0 -2px 4px rgba(0,0,0,0.1);
    margin-bottom: 0;
}

.unified-document-footer.company-footer-image-container img {
    max-height: calc(var(--footer-height) - 20px);
    max-width: 100%;
    object-fit: contain;
    margin-bottom: 2px;
}
/* #endregion */

/* #region 模板样式 - 不同文档模板的主题样式 */
.template-classic {
    font-family: var(--classic-font-family);
    color: #333;
}

.template-classic .header {
    border-bottom: 1px solid #ddd;
}

.template-classic .table th {
    background-color: #f5f5f5;
}

.template-classic .table td,
.template-classic .table th {
    border: 1px solid #ddd;
}

.template-modern {
    font-family: var(--base-font-family);
    color: #2d3748;
}

.template-modern .header {
    border-bottom: 2px solid var(--secondary-color);
}

.template-modern .table th {
    background-color: var(--secondary-color);
    color: white;
}

.template-modern .table td,
.template-modern .table th {
    border: none;
    border-bottom: 1px solid #e2e8f0;
}

.template-elegant {
    font-family: var(--elegant-font-family);
    color: #1a202c;
}

.template-elegant .header {
    border-bottom: double 3px #805ad5;
}

.template-elegant .table th {
    background-color: #faf5ff;
    color: #6b46c1;
}

.template-elegant .table td,
.template-elegant .table th {
    border: 1px solid #e9d8fd;
}

.template-tourism {
    font-family: var(--tourism-font-family);
    color: #2c5282;
}

.template-tourism .header {
    background: linear-gradient(to right, #90cdf4, #4299e1);
    color: white;
}

.template-tourism .table th {
    background-color: #4299e1;
    color: white;
}

.template-tourism .table td,
.template-tourism .table th {
    border: 1px solid #bee3f8;
}

/* 模板强调条样式 */
.template-accent-bar {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(to right, var(--primary-color), var(--accent-color));
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
}
/* #endregion */

/* #region 文档内容样式 */
.document-title-section {
    padding: 15px 25px;
    text-align: center;
}

.document-title-section h1 {
    font-size: var(--title-font-size);
    margin-bottom: 10px;
    color: var(--primary-color);
    font-weight: bold;
}

.client-section,
.items-section,
.payment-notes-section {
    padding: 15px 25px;
}

.client-section {
    background-color: var(--light-color);
}

/* 表格样式 */
.items-section table,
.data-table {
    width: 100%;
    border-collapse: collapse;
    margin: 1em 0;
    font-size: var(--small-font-size);
}

.items-section th,
.items-section td,
.data-table th,
.data-table td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
    border: 1px solid #d1d5db;
    vertical-align: top;
}

.items-section th,
.data-table th {
    background-color: var(--light-color);
    font-weight: bold;
    font-size: 10pt;
    padding: 6px 4px;
    color: #374151;
}

.items-section td,
.data-table td {
    font-size: 10pt;
    padding: 4px;
}

.data-table tbody tr:nth-child(even),
.items-section table tbody tr:nth-child(even) {
    background-color: #f9fafb;
}

.data-table tfoot,
.items-section table tfoot {
    background-color: var(--light-color);
    font-weight: 600;
}

/* 金额列右对齐 */
.amount-column,
.data-table .text-right,
.items-section table .text-right {
    text-align: right;
}

/* 客户信息样式 */
.customer-info,
.customer-info p,
.customer-info span,
.customer-info div {
    font-size: 10pt;
}

.customer-info h3 {
    font-size: 11pt;
    margin-bottom: 4px;
}

/* 客户信息和买方信息布局 */
.customer-buyer-info,
.client-section .grid {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    gap: 20px;
}

.customer-info-left,
.buyer-info-right,
.customer-info,
.buyer-info {
    width: calc(50% - 10px);
    padding: 12px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    box-sizing: border-box;
    background-color: #f9fafb;
}

.customer-info h3,
.buyer-info h3 {
    font-size: var(--small-font-size);
    font-weight: 600;
    margin-bottom: 8px;
    color: #6b7280;
}

.customer-info .p-4,
.buyer-info .p-4 {
    padding: 8px;
    background-color: white;
    border-radius: 4px;
    font-size: var(--small-font-size);
}

/* 备注和付款方式样式 */
.notes-section p,
#notes-preview {
    font-size: var(--small-font-size);
}

.payment-method p,
#payment-method-preview {
    font-size: var(--small-font-size);
}

.payment-method h3 {
    font-size: 10pt;
    margin-bottom: 2px;
}

.notes-section,
.payment-method-section {
    margin-bottom: 15px;
}

.notes-section h4,
.payment-method-section h4 {
    font-size: var(--small-font-size);
    font-weight: 600;
    margin-bottom: 6px;
    color: #6b7280;
    text-transform: uppercase;
}

.notes-section p,
.payment-method-section p {
    font-size: var(--small-font-size);
    line-height: 1.4;
    color: #374151;
    margin: 0;
}

/* 签名区域 */
.signature-section {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

.signature-line {
    border-bottom: 1px solid #374151;
    height: 40px;
    margin-bottom: 8px;
}

.company-name-signature {
    font-size: var(--small-font-size);
    font-weight: 600;
    color: #374151;
}
/* #endregion */

/* #region 图片样式 - 整合自template-image.css */
/* 公司Logo样式 */
.company-logo {
    max-height: 70px;
    max-width: 80%;
    object-fit: contain;
    margin: 0 auto;
    display: block;
    transition: var(--transition-normal);
}

.company-header-image {
    max-height: none !important;
    height: auto !important;
    width: auto !important;
    max-width: 100% !important;
    object-fit: contain !important;
}

.company-logo[data-company="smw"] {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.company-logo[data-company="gmh"] {
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* 页眉页脚图片样式 */
.header-image,
.footer-image {
    display: block;
    width: 100%;
    height: auto;
    margin: 0 auto;
    object-fit: contain;
    max-height: auto;
}

.header-image {
    margin-bottom: 5px;
}

.footer-image {
    margin-top: 10px;
    opacity: 0.9;
    max-height: 60px;
}

.company-logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80px;
    overflow: hidden;
}

/* 公司信息样式 */
.company-info-container {
    margin: 0 auto 10px;
    padding: 0 0 10px;
    border-bottom: 1px solid var(--border-color);
    max-width: 90%;
}

.company-info {
    display: flex;
    flex-direction: column;
    margin: 0;
    padding: 0;
    font-size: 9pt;
    line-height: 1.3;
    color: #6b7280;
}

.company-info-item {
    margin: 2px 0;
    padding: 0;
    white-space: normal;
    overflow: hidden;
    text-overflow: ellipsis;
}

.company-address {
    font-style: normal;
    margin-top: 5px;
}

/* 模板背景图片样式 */
.template-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.05;
    pointer-events: none;
    z-index: 0;
    object-fit: cover;
}

.template-background[data-template="classic"] {
    opacity: 0.03;
    background-position: center;
}

.template-background[data-template="modern"] {
    opacity: 0.04;
    background-position: top right;
}

.template-background[data-template="elegant"] {
    opacity: 0.05;
    background-position: center;
}

.template-background[data-template="tourism"] {
    opacity: 0.07;
    background-position: bottom center;
}
/* #endregion */

/* #region 印章样式 - 整合自stamp-position.css */
.company-stamp {
    position: absolute !important;
    width: 80px !important;
    height: 80px !important;
    top: calc(var(--a4-height-px) * 0.8 - 80px) !important;
    right: 40px !important;
    padding: 0 !important;
    margin: 0 !important;
    z-index: var(--z-index-stamp) !important;
    pointer-events: none !important;
    display: block !important;
    text-align: right;
    visibility: visible !important;
    page-break-inside: avoid !important;
}

.company-stamp img,
img.stamp-image,
img.company-stamp-gmh,
img.company-stamp-smw,
img.driver-agreement-stamp-image {
    display: block !important;
    position: relative !important;
    top: 0 !important;
    right: 0 !important;
    width: 80px !important;
    height: 80px !important;
    max-width: 80px !important;
    max-height: 80px !important;
    border-radius: 50% !important;
    object-fit: contain !important;
    z-index: var(--z-index-stamp) !important;
    opacity: 0.85 !important;
    transform: rotate(-5deg) !important;
    margin: 0 auto !important;
    background-color: transparent !important;
    visibility: visible !important;
}

/* 导出文档中的印章样式 */
.pdf-export-container .company-stamp,
.image-export-container .company-stamp {
    position: absolute !important;
    top: 158mm !important;
    right: 22mm !important;
    width: 50mm !important;
    height: 30mm !important;
    z-index: var(--z-index-stamp) !important;
}

.pdf-export-container .company-stamp img,
.pdf-export-container img.stamp-image,
.pdf-export-container img.company-stamp-gmh,
.pdf-export-container img.company-stamp-smw,
.pdf-export-container img.driver-agreement-stamp-image,
.image-export-container .company-stamp img,
.image-export-container img.stamp-image,
.image-export-container img.company-stamp-gmh,
.image-export-container img.company-stamp-smw,
.image-export-container img.driver-agreement-stamp-image {
    position: absolute !important;
    top: 2.5mm !important;
    right: 12.5mm !important;
    width: 25mm !important;
    height: 25mm !important;
    border-radius: 50% !important;
    object-fit: contain !important;
    z-index: var(--z-index-stamp) !important;
    opacity: 0.85 !important;
    visibility: visible !important;
    display: block !important;
}
/* #endregion */

/* #region 司机协议样式 - 整合自driver-agreement.css */
#specific-driver-agreement-render-area {
    position: relative;
    width: 100%;
    max-width: var(--a4-width-px);
    min-height: var(--a4-height-px);
    margin: 0 auto;
    padding: 0;
    background-color: white;
    box-sizing: border-box;
    font-family: 'Times New Roman', Times, serif;
    color: #000;
    border: 1px solid #e0e0e0;
    box-shadow: var(--box-shadow-light);
    display: flex;
    flex-direction: column;
}

#driver-agreement-content {
    flex: 1;
    padding: 10px 20px 30px 20px;
    position: relative;
    width: 100%;
    box-sizing: border-box;
    background-color: white;
}

/* 协议主标题 */
#driver-agreement-content .document-title h1 {
    font-size: 14pt;
    text-align: center;
    margin-top: 5px;
    margin-bottom: 5px;
    font-weight: bold;
    color: #000;
}

/* 协议类型标题 */
#driver-agreement-content h2.agreement-type-title {
    font-size: 16pt;
    text-align: center;
    margin-top: 0;
    margin-bottom: 5px;
    font-weight: bold;
    color: #000;
}

/* 协议编号样式 */
.agreement-meta-info {
    text-align: right;
    font-size: 8pt;
    margin-bottom: 10px;
    color: #000;
}

/* 协议方信息样式 */
.parties-info {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 8pt;
    color: #000;
}

.party-info {
    width: 48%;
    border: 1px solid #000;
    padding: 5px;
    box-sizing: border-box;
}

.party-info h3 {
    font-size: 8pt;
    font-weight: bold;
    margin: 0 0 5px 0;
    color: #000;
}

.party-info p {
    margin: 0 0 3px 0;
    font-size: 8pt;
    text-align: left;
    color: #000;
}

/* 协议内容区域 */
.agreement-content {
    display: flex;
    justify-content: space-between;
    gap: 20px;
    margin-bottom: 15px;
    font-size: 8.5pt;
    line-height: 1.4;
    color: #333;
}

.agreement-left-column,
.agreement-right-column {
    width: 48%;
}

.agreement-section h2 {
    font-size: 10pt;
    font-weight: bold;
    color: #111;
    margin-top: 12px;
    margin-bottom: 6px;
    padding-bottom: 2px;
    border-bottom: 1px solid #ccc;
}

.agreement-section p,
.agreement-section li {
    font-size: 8.5pt;
    text-align: justify;
    margin-bottom: 5px;
}

.agreement-section ol {
    padding-left: 18px;
    margin-top: 3px;
    margin-bottom: 8px;
}

.agreement-section ol li {
    margin-bottom: 4px;
}

/* 电子生成提示样式 */
.electronic-generated-notice {
    width: 100%;
    margin: 5px 0 10px 0;
    padding: 0;
    font-size: 7pt;
    color: #333;
    text-align: center;
    border-top: none;
    box-sizing: border-box;
}

.electronic-generated-notice p {
    text-align: center;
    margin: 0;
    padding: 0;
}

/* 页脚样式 */
.company-footer-image-container {
    display: block;
    margin-top: 20px;
    text-align: center;
}

.company-footer-image {
    max-width: 100%;
}

/* 司机协议文本样式 */
.driver-agreement-text {
    font-size: 60%;
    padding-left: 5px;
    padding-right: 5px;
    line-height: 1.2;
    max-width: 100%;
    margin: 0;
}

.driver-agreement-text h1 {
    font-size: 1rem;
    margin-top: 5px;
    margin-bottom: 5px;
    line-height: 1.1;
    text-align: center;
    font-weight: bold;
    padding: 0;
}

.driver-agreement-text h2 {
    font-size: 0.8rem;
    margin-top: 4px;
    margin-bottom: 2px;
    line-height: 1;
    font-weight: bold;
    padding: 0;
}

.driver-agreement-text .section {
    margin-bottom: 3px;
    margin-top: 3px;
    padding: 0;
}

.driver-agreement-text p {
    margin-bottom: 2px;
    margin-top: 0;
    line-height: 1.2;
    font-size: 0.7rem;
    padding: 0;
}
/* #endregion */

/* #region 响应式设计 */
@media (max-width: 1400px) {
    #document-preview {
        --preview-scale-factor: 0.7;
    }
}

@media (max-width: 1200px) {
    #document-preview {
        --preview-scale-factor: 0.6;
    }
}

@media (max-width: 992px) {
    #document-preview {
        --preview-scale-factor: 0.5;
    }
}

@media (max-width: 768px) {
    .a4-page, #document-container {
        width: 100%;
        max-width: var(--a4-width-px);
    }

    #document-preview {
        --preview-scale-factor: var(--preview-scale-min);
        margin: 10px auto;
        transform: scale(0.8);
    }

    #document-container {
        padding-top: 200px;
        padding-bottom: 150px;
    }

    .document-header {
        height: 180px;
    }

    .document-footer {
        height: 100px;
    }

    .document-title-section,
    .client-section,
    .items-section,
    .payment-notes-section {
        padding: 10px 15px;
    }

    /* 移动端客户信息垂直排列 */
    .customer-buyer-info,
    .client-section .grid {
        flex-direction: column;
        gap: 10px;
    }

    .customer-info-left,
    .buyer-info-right,
    .customer-info,
    .buyer-info {
        width: 100%;
    }

    /* 移动端图片样式调整 */
    .company-logo {
        width: 100%;
    }

    .stamp-image {
        max-width: 70px;
        max-height: 70px;
    }

    .header-image {
        width: 100%;
        margin-bottom: 1rem;
    }

    .footer-image {
        width: 100%;
        margin-top: 1rem;
    }

    .company-info {
        font-size: 8pt;
    }

    .company-info-container {
        max-width: 100%;
        padding: 0 0 5px;
        margin-bottom: 5px;
    }

    /* 移动端印章样式调整 */
    #document-preview .company-stamp {
        position: relative !important;
        top: auto !important;
        right: auto !important;
        margin-left: auto !important;
        margin-right: 20px !important;
        text-align: right !important;
    }

    #document-preview .company-stamp img,
    #document-preview img.stamp-image,
    #document-preview img.company-stamp-gmh,
    #document-preview img.company-stamp-smw,
    #document-preview img.driver-agreement-stamp-image {
        position: relative !important;
        top: auto !important;
        right: auto !important;
        width: 90px !important;
        height: 90px !important;
        margin-left: auto !important;
    }

    /* 移动端表格样式 */
    .data-table,
    .items-section table {
        font-size: 10px;
    }

    .data-table th,
    .data-table td,
    .items-section table th,
    .items-section table td {
        padding: 6px 8px;
    }
}

@media (max-width: 480px) {
    #document-preview {
        transform: scale(0.7);
    }

    .document-title-section h1 {
        font-size: 16pt;
    }

    .items-section th,
    .items-section td {
        padding: 4px 2px;
        font-size: 9pt;
    }

    /* 小屏幕印章调整 */
    #document-preview .company-stamp img,
    #document-preview img.stamp-image,
    #document-preview img.company-stamp-gmh,
    #document-preview img.company-stamp-smw,
    #document-preview img.driver-agreement-stamp-image {
        width: 70px !important;
        height: 70px !important;
    }

    .stamp-image {
        max-width: 60px;
        max-height: 60px;
    }
}
/* #endregion */

/* #region 打印样式 */
@media print {
    body, html {
        margin: 0;
        padding: 0;
        background-color: white !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    #document-preview {
        transform: scale(1) !important;
        margin: 0 !important;
        padding: 0 !important;
        box-shadow: none !important;
        border: none !important;
        width: var(--a4-width-px) !important;
        height: var(--a4-height-px) !important;
        max-width: none !important;
        min-height: unset !important;
        aspect-ratio: unset !important;
        overflow: hidden !important;
    }

    #document-container {
        padding-top: var(--header-height) !important;
        padding-bottom: 0 !important;
        box-shadow: none !important;
        border: none !important;
        height: var(--a4-height-px) !important;
        min-height: unset !important;
        display: flex !important;
        flex-direction: column !important;
        page-break-inside: avoid !important;
        overflow: hidden !important;
    }

    .document-header-image-container,
    .document-header,
    .document-footer,
    .company-footer-image-container {
        position: fixed !important;
        left: 0 !important;
        right: 0 !important;
        width: 100% !important;
        transform: none !important;
        box-shadow: none !important;
        background-color: white !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    .document-header-image-container,
    .document-header {
        top: 0 !important;
        height: var(--header-height) !important;
        padding: 5px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        page-break-after: avoid !important;
        page-break-inside: avoid !important;
        margin: 0 !important;
    }

    .document-footer,
    .company-footer-image-container {
        bottom: 0 !important;
        height: var(--footer-height) !important;
        margin-bottom: 0 !important;
        padding-bottom: 0 !important;
        page-break-after: avoid !important;
        page-break-inside: avoid !important;
    }

    .document-header-image-container img,
    .document-footer img {
        max-height: 100% !important;
        max-width: 100% !important;
        width: auto !important;
        height: auto !important;
        object-fit: contain !important;
        display: block !important;
        margin: 0 auto !important;
        padding: 0 !important;
    }

    .company-footer-image-container img {
        max-height: 100%;
        max-width: 100%;
        padding: 5px 5px 0 5px;
        margin-bottom: 0;
    }

    /* 印章打印样式 */
    .company-stamp {
        position: absolute !important;
        top: 208mm !important;
        right: 42mm !important;
        page-break-inside: avoid !important;
        min-height: 30mm;
    }

    /* 隐藏非打印元素 */
    header,
    nav,
    .preview-header,
    .fab,
    .template-accent-bar,
    .no-print,
    .no-print * {
        display: none !important;
    }

    /* 确保内容区域正确分页 */
    .items-section table {
        page-break-inside: auto;
    }

    .items-section tr {
        page-break-inside: avoid;
        page-break-after: auto;
    }

    .document-title-section,
    .client-section,
    .items-section,
    .payment-notes-section {
        page-break-after: auto;
        page-break-inside: avoid;
    }

    /* 司机协议打印样式 */
    #specific-driver-agreement-render-area {
        width: 100% !important;
        height: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        box-shadow: none !important;
    }

    #driver-agreement-content {
        padding: 10px 15px 80px 15px !important;
    }

    #specific-driver-agreement-render-area .document-header-image-container {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        border-bottom: 2px solid purple !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    .company-info-container {
        border-bottom: 1px solid #000 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }

    .parties-info .party-info {
        border: 1px solid #000 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
        background-color: #fff;
        box-shadow: none;
        break-inside: avoid;
    }

    .electronic-generated-notice {
        color: #333 !important;
        font-size: 7pt !important;
    }

    .agreement-meta-info {
        background-color: #fff;
        border-bottom: 1px solid #eee;
    }

    #specific-driver-agreement-render-area #driver-agreement-content {
        padding-top: 60px !important;
    }
}

/* 多页文档分页 */
@page {
    margin: 0;
    size: A4;
}
/* #endregion */
