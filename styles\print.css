/**
 * @file SmartOffice 2.0 打印专用样式
 * @description 专门用于打印的样式文件，确保文档在打印时的完美呈现
 * @version 3.0.0 - 第三阶段重构版本
 * <AUTHOR> Team
 */

/* #region 打印页面设置 */
@page {
    margin: 0;
    size: A4 portrait;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
}

@page :first {
    margin-top: 0;
}

@page :left {
    margin-left: 0;
}

@page :right {
    margin-right: 0;
}
/* #endregion */

/* #region 打印基础样式 */
@media print {
    * {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
        color-adjust: exact !important;
    }
    
    body, html {
        margin: 0 !important;
        padding: 0 !important;
        background: white !important;
        font-size: 12pt !important;
        line-height: 1.4 !important;
        color: #000 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    
    /* 隐藏所有非打印元素 */
    header, nav, footer,
    .no-print, .screen-only,
    .preview-controls, .toolbar,
    .sidebar, .navigation,
    button:not(.print-button),
    input, select, textarea,
    .fab, .floating-action-button,
    .tooltip, .modal, .popup,
    .debug, .development-only {
        display: none !important;
        visibility: hidden !important;
    }
    
    /* 确保打印内容可见 */
    .print-only {
        display: block !important;
        visibility: visible !important;
    }
    
    /* 移除所有阴影和边框效果 */
    * {
        box-shadow: none !important;
        text-shadow: none !important;
        border-radius: 0 !important;
    }
    
    /* 确保链接在打印时显示 */
    a {
        color: #000 !important;
        text-decoration: none !important;
    }
    
    a[href]:after {
        content: " (" attr(href) ")";
        font-size: 10pt;
        color: #666;
    }
    
    /* 图片打印优化 */
    img {
        max-width: 100% !important;
        height: auto !important;
        page-break-inside: avoid !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    
    /* 表格打印优化 */
    table {
        border-collapse: collapse !important;
        width: 100% !important;
        page-break-inside: auto !important;
    }
    
    thead {
        display: table-header-group !important;
    }
    
    tfoot {
        display: table-footer-group !important;
    }
    
    tr {
        page-break-inside: avoid !important;
    }
    
    th, td {
        border: 1px solid #000 !important;
        padding: 4pt 6pt !important;
        text-align: left !important;
        vertical-align: top !important;
    }
    
    th {
        background-color: #f0f0f0 !important;
        font-weight: bold !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    
    /* 分页控制 */
    .page-break-before {
        page-break-before: always !important;
    }
    
    .page-break-after {
        page-break-after: always !important;
    }
    
    .page-break-inside-avoid {
        page-break-inside: avoid !important;
    }
    
    .keep-together {
        page-break-inside: avoid !important;
    }
    
    /* 标题打印样式 */
    h1, h2, h3, h4, h5, h6 {
        page-break-after: avoid !important;
        page-break-inside: avoid !important;
        margin-top: 0 !important;
        color: #000 !important;
        font-weight: bold !important;
    }
    
    h1 {
        font-size: 18pt !important;
        margin-bottom: 12pt !important;
    }
    
    h2 {
        font-size: 16pt !important;
        margin-bottom: 10pt !important;
    }
    
    h3 {
        font-size: 14pt !important;
        margin-bottom: 8pt !important;
    }
    
    h4, h5, h6 {
        font-size: 12pt !important;
        margin-bottom: 6pt !important;
    }
    
    /* 段落打印样式 */
    p {
        margin: 0 0 6pt 0 !important;
        orphans: 3 !important;
        widows: 3 !important;
        text-align: justify !important;
    }
    
    /* 列表打印样式 */
    ul, ol {
        margin: 6pt 0 !important;
        padding-left: 20pt !important;
    }
    
    li {
        margin-bottom: 3pt !important;
        page-break-inside: avoid !important;
    }
    
    /* 代码和预格式化文本 */
    pre, code {
        font-family: 'Courier New', monospace !important;
        font-size: 10pt !important;
        background: #f5f5f5 !important;
        border: 1px solid #ccc !important;
        padding: 4pt !important;
        page-break-inside: avoid !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    
    /* 引用样式 */
    blockquote {
        margin: 12pt 20pt !important;
        padding: 6pt 12pt !important;
        border-left: 3pt solid #ccc !important;
        font-style: italic !important;
        page-break-inside: avoid !important;
    }
    
    /* 水平分割线 */
    hr {
        border: none !important;
        border-top: 1pt solid #000 !important;
        margin: 12pt 0 !important;
        page-break-after: avoid !important;
    }
}
/* #endregion */

/* #region SmartOffice 特定打印样式 */
@media print {
    /* 文档容器打印样式 */
    #document-preview,
    #document-container,
    .a4-page {
        width: 210mm !important;
        height: 297mm !important;
        margin: 0 !important;
        padding: 0 !important;
        transform: none !important;
        overflow: visible !important;
        background: white !important;
        border: none !important;
        box-shadow: none !important;
    }
    
    /* 页眉页脚打印固定 */
    .document-header,
    .document-header-image-container {
        position: running(header) !important;
        width: 100% !important;
        height: auto !important;
        margin: 0 !important;
        padding: 5mm !important;
        background: white !important;
        border-bottom: 1pt solid #ccc !important;
    }
    
    .document-footer,
    .company-footer-image-container {
        position: running(footer) !important;
        width: 100% !important;
        height: auto !important;
        margin: 0 !important;
        padding: 5mm !important;
        background: white !important;
        border-top: 1pt solid #ccc !important;
    }
    
    /* 印章打印位置 */
    .company-stamp {
        position: absolute !important;
        bottom: 50mm !important;
        right: 20mm !important;
        width: 25mm !important;
        height: 25mm !important;
        z-index: 1000 !important;
    }
    
    .company-stamp img {
        width: 100% !important;
        height: 100% !important;
        object-fit: contain !important;
        opacity: 0.8 !important;
    }
    
    /* 司机协议打印特殊样式 */
    #specific-driver-agreement-render-area {
        width: 210mm !important;
        height: 297mm !important;
        margin: 0 !important;
        padding: 15mm !important;
        font-size: 9pt !important;
        line-height: 1.3 !important;
    }
    
    .agreement-content {
        column-count: 2 !important;
        column-gap: 10mm !important;
        column-rule: 1pt solid #ccc !important;
    }
    
    .parties-info {
        column-span: all !important;
        margin-bottom: 10pt !important;
    }
    
    .party-info {
        border: 1pt solid #000 !important;
        padding: 6pt !important;
        margin-bottom: 6pt !important;
        background: #f9f9f9 !important;
        -webkit-print-color-adjust: exact;
        print-color-adjust: exact;
    }
    
    /* 电子生成提示 */
    .electronic-generated-notice {
        position: absolute !important;
        bottom: 10mm !important;
        right: 10mm !important;
        font-size: 7pt !important;
        color: #666 !important;
        font-style: italic !important;
    }
}
/* #endregion */

/* #region 打印质量优化 */
@media print {
    /* 高质量图片打印 */
    img {
        image-rendering: -webkit-optimize-contrast !important;
        image-rendering: optimize-contrast !important;
        image-rendering: crisp-edges !important;
    }
    
    /* 文本渲染优化 */
    * {
        text-rendering: optimizeLegibility !important;
        -webkit-font-smoothing: antialiased !important;
        -moz-osx-font-smoothing: grayscale !important;
    }
    
    /* 颜色和背景强制打印 */
    .force-print-background {
        -webkit-print-color-adjust: exact !important;
        print-color-adjust: exact !important;
        color-adjust: exact !important;
    }
    
    /* 边框和线条优化 */
    .print-border {
        border: 1pt solid #000 !important;
    }
    
    .print-border-light {
        border: 0.5pt solid #666 !important;
    }
    
    .print-border-dashed {
        border: 1pt dashed #000 !important;
    }
}
/* #endregion */
