<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SmartOffice 2.0 - 智能办公文档生成系统</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- 外部依赖库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    
    <!-- 项目核心模块 - 按依赖顺序加载 -->
    <script src="js/logger.js"></script>
    <script src="js/image-base64.js"></script>
    
    <!-- 核心服务模块 -->
    <script src="modules/services/notification-manager.js"></script>
    <script src="modules/services/api-status-manager.js"></script>
    <script src="modules/services/form-data-manager.js"></script>
    <script src="modules/services/app-initializer-manager.js"></script>
    <script src="modules/services/resource-manager.js"></script>
    <script src="modules/services/inline-component-loader.js"></script>
    
    <!-- 业务逻辑模块 -->
    <script src="js/document-renderer.js"></script>
    <script src="js/image-manager.js"></script>
    <script src="js/export-manager.js"></script>
    <script src="js/nlp-processor.js"></script>
    <script src="js/app.js"></script>
    
    <!-- 统一样式文件 -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/print.css" media="print">
</head>
<body class="bg-gray-100">
    <!-- 顶部导航栏 -->
    <nav class="bg-blue-600 text-white p-4">
        <div class="container mx-auto flex justify-between items-center">
            <h1 class="text-xl font-bold">SmartOffice 2.0</h1>
            <div id="api-status-indicator" class="text-sm bg-yellow-700 text-yellow-100 px-3 py-1 rounded-full flex items-center">
                <span class="w-2 h-2 bg-yellow-300 rounded-full mr-2"></span>
                初始化中...
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container mx-auto p-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            
            <!-- 左侧：输入区域 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <h2 class="text-lg font-semibold mb-4">文档信息输入</h2>
                
                <!-- 文档类型选择 -->
                <div class="mb-4">
                    <label for="document-type" class="block text-sm font-medium text-gray-700 mb-2">文档类型</label>
                    <select id="document-type" class="w-full p-2 border border-gray-300 rounded-md">
                        <option value="receipt">收据</option>
                        <option value="invoice">发票</option>
                        <option value="quotation">报价单</option>
                        <option value="driver_agreement">司机协议</option>
                    </select>
                </div>
                
                <!-- 公司选择 -->
                <div class="mb-4">
                    <label for="company-selector" class="block text-sm font-medium text-gray-700 mb-2">公司</label>
                    <select id="company-selector" class="w-full p-2 border border-gray-300 rounded-md">
                        <option value="gomyhire">GoMyHire</option>
                        <option value="sky-mirror">Sky Mirror</option>
                    </select>
                </div>
                
                <!-- 文本输入区域 -->
                <div class="mb-4">
                    <label for="text-input" class="block text-sm font-medium text-gray-700 mb-2">文档内容</label>
                    <textarea id="text-input" rows="8" class="w-full p-3 border border-gray-300 rounded-md" 
                              placeholder="请输入文档内容或上传图片进行识别..."></textarea>
                </div>
                
                <!-- 图片上传区域 -->
                <div class="mb-4">
                    <label for="image-input" class="block text-sm font-medium text-gray-700 mb-2">图片上传</label>
                    <input type="file" id="image-input" accept="image/*" class="w-full p-2 border border-gray-300 rounded-md">
                </div>
                
                <!-- API密钥配置 -->
                <div class="mb-4">
                    <label for="api-key-input" class="block text-sm font-medium text-gray-700 mb-2">Gemini API密钥</label>
                    <input type="password" id="api-key-input" class="w-full p-2 border border-gray-300 rounded-md" 
                           placeholder="请输入Gemini API密钥">
                </div>
                
                <!-- 操作按钮 -->
                <div class="flex space-x-2">
                    <button id="process-button" class="bg-blue-500 text-white px-4 py-2 rounded-md hover:bg-blue-600">
                        处理文档
                    </button>
                    <button id="clear-button" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600">
                        清空
                    </button>
                </div>
            </div>
            
            <!-- 右侧：预览区域 -->
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-semibold">文档预览</h2>
                    <div class="flex space-x-2">
                        <!-- 缩放控制 -->
                        <button id="zoom-out-button" class="bg-gray-500 text-white px-2 py-1 rounded text-sm">-</button>
                        <span id="zoom-level" class="text-sm px-2 py-1">75%</span>
                        <button id="zoom-in-button" class="bg-gray-500 text-white px-2 py-1 rounded text-sm">+</button>
                        <button id="zoom-reset-button" class="bg-gray-500 text-white px-2 py-1 rounded text-sm">重置</button>
                        
                        <!-- 打印范围指示器切换 -->
                        <button id="toggle-print-range" class="bg-green-500 text-white px-3 py-1 rounded text-sm">
                            显示打印范围
                        </button>
                    </div>
                </div>
                
                <!-- 预览容器 -->
                <div id="preview-container" class="border border-gray-300 rounded-md overflow-auto">
                    <div id="document-container" class="a4-page">
                        <div class="p-8 text-center text-gray-500">
                            请输入文档内容或上传图片开始生成文档
                        </div>
                        
                        <!-- 打印范围指示器 -->
                        <div id="print-range-indicator" class="print-range-indicator hidden">
                            <!-- 打印范围边框和标签将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>
                
                <!-- 导出按钮 -->
                <div class="flex space-x-2 mt-4">
                    <button id="export-image-button" class="bg-green-500 text-white px-4 py-2 rounded-md hover:bg-green-600">
                        导出图片
                    </button>
                    <button id="export-pdf-button" class="bg-red-500 text-white px-4 py-2 rounded-md hover:bg-red-600">
                        导出PDF
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 应用启动脚本 -->
    <script>
        // 传统架构应用启动脚本
        document.addEventListener('DOMContentLoaded', async function() {
            try {
                console.log('🚀 启动SmartOffice 2.0 传统架构版本...');

                // 初始化应用
                if (typeof initializeInlineComponents === 'function') {
                    await initializeInlineComponents();
                }
                
                if (typeof initializeInlineResourceManager === 'function') {
                    await initializeInlineResourceManager();
                }
                
                // 初始化应用管理器
                if (typeof window.appInitializerManager !== 'undefined') {
                    await window.appInitializerManager.initialize();
                }

                console.log('✅ SmartOffice 2.0 传统架构版本启动完成');

            } catch (error) {
                console.error('❌ 应用启动失败:', error);

                // 显示错误信息
                document.body.innerHTML = `
                    <div style="padding: 20px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 4px; margin: 20px; color: #721c24;">
                        <h2>❌ SmartOffice 2.0 启动失败</h2>
                        <p><strong>错误原因:</strong> ${error.message}</p>
                        <p><strong>可能的解决方案:</strong></p>
                        <ul>
                            <li>检查网络连接</li>
                            <li>刷新页面重试</li>
                            <li>检查浏览器控制台获取详细错误信息</li>
                            <li>确保所有JavaScript文件已正确加载</li>
                        </ul>
                        <button type="button" onclick="location.reload()" style="background: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-top: 10px;">
                            重新加载页面
                        </button>
                    </div>
                `;
            }
        });
    </script>

    <!-- 注意：原有的大量内联JavaScript代码已移除，功能已模块化到独立文件中 -->
    <!-- 如需查看完整功能，请参考 modules/ 目录下的各个模块文件 -->
</body>
</html>
