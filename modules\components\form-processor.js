/**
 * @file 表单处理器模块 - 从index.html提取的表单处理功能
 * @description 提供表单数据处理、验证和填充功能
 */

import { getLogger } from '../core/logger.js';
import { showSuccessNotification, showWarningNotification } from './event-handlers.js';

// #region 全局变量
const pageLogger = getLogger();
// #endregion

// #region 表单处理器类
/**
 * @class FormProcessor - 表单处理器
 * @description 处理表单数据的填充、验证和管理
 */
export class FormProcessor {
    /**
     * 构造函数 - 初始化表单处理器
     */
    constructor() {
        this.formData = {};
        this.validationRules = {};
        
        pageLogger.info('FormProcessor', 'constructor', '📝 表单处理器已创建');
        this._initializeFormHandlers();
    }

    /**
     * 从NLP结果填充表单
     * @param {Object} extractedData - 提取的数据
     */
    fillFormFromNLPResult(extractedData) {
        pageLogger.debug('FormProcessor', 'fillFormFromNLPResult', '开始填充表单字段', {
            fieldsCount: Object.keys(extractedData).length,
            extractedData
        });

        try {
            // 基础字段映射
            const fieldMappings = {
                customerName: 'customer-name',
                channel: 'channel',
                totalAmount: 'total-amount-display',
                issueDate: 'receipt-date-input',
                receiptNumber: 'receipt-number-input',
                notes: 'notes',
                conclusionTextCn: 'conclusion-text-cn',
                conclusionTextEn: 'conclusion-text-en'
            };

            // 填充基础字段
            for (const [dataKey, elementId] of Object.entries(fieldMappings)) {
                if (extractedData[dataKey]) {
                    this._fillField(elementId, extractedData[dataKey]);
                }
            }

            // 填充买方信息
            if (extractedData.buyerInfo) {
                this._fillBuyerInfo(extractedData.buyerInfo);
            }

            // 填充服务项目
            if (extractedData.services && Array.isArray(extractedData.services)) {
                this._fillServiceItems(extractedData.services, extractedData);
            }

            // 填充文档特定字段
            const documentType = this._getCurrentDocumentType();
            this._fillDocumentSpecificFields(extractedData, documentType);

            // 更新总金额显示
            if (extractedData.totalAmount) {
                this._updateTotalAmount(extractedData.totalAmount);
            }

            // 强制更新预览
            if (typeof window.updateDocumentPreview === 'function') {
                window.updateDocumentPreview();
            }

            pageLogger.info('FormProcessor', 'fillFormFromNLPResult', '✅ 表单填充完成');
            showSuccessNotification('智能填充完成', '表单字段已自动填充');

        } catch (error) {
            pageLogger.error('FormProcessor', 'fillFormFromNLPResult', '❌ 表单填充失败', error);
            showWarningNotification('填充失败', '部分字段填充失败，请手动检查');
        }
    }

    /**
     * 验证表单数据
     * @returns {Object} 验证结果
     */
    validateForm() {
        const errors = [];
        const warnings = [];

        try {
            // 验证必填字段
            const requiredFields = {
                'customer-name': '客户名称',
                'receipt-date-input': '日期'
            };

            for (const [fieldId, fieldName] of Object.entries(requiredFields)) {
                const element = document.getElementById(fieldId);
                if (!element || !element.value.trim()) {
                    errors.push(`${fieldName}不能为空`);
                }
            }

            // 验证金额格式
            const totalAmountElement = document.getElementById('total-amount-display');
            if (totalAmountElement) {
                const amount = parseFloat(totalAmountElement.textContent);
                if (isNaN(amount) || amount <= 0) {
                    warnings.push('总金额应该大于0');
                }
            }

            // 验证日期格式
            const dateElement = document.getElementById('receipt-date-input');
            if (dateElement && dateElement.value) {
                const date = new Date(dateElement.value);
                if (isNaN(date.getTime())) {
                    errors.push('日期格式不正确');
                }
            }

            // 验证服务项目
            const serviceItems = this._getServiceItems();
            if (serviceItems.length === 0) {
                warnings.push('建议至少添加一个服务项目');
            }

            return {
                isValid: errors.length === 0,
                errors,
                warnings,
                fieldCount: this._getFilledFieldCount()
            };

        } catch (error) {
            pageLogger.error('FormProcessor', 'validateForm', '表单验证失败', error);
            return {
                isValid: false,
                errors: ['表单验证过程中发生错误'],
                warnings: [],
                fieldCount: 0
            };
        }
    }

    /**
     * 重置表单
     */
    resetForm() {
        try {
            pageLogger.info('FormProcessor', 'resetForm', '开始重置表单');

            // 重置文本输入框
            const textInputs = document.querySelectorAll('input[type="text"], input[type="date"], input[type="number"], textarea');
            textInputs.forEach(input => {
                if (input.id !== 'conclusion-text-cn' && input.id !== 'conclusion-text-en') {
                    input.value = '';
                }
            });

            // 重置选择框
            const selects = document.querySelectorAll('select');
            selects.forEach(select => {
                select.selectedIndex = 0;
            });

            // 重置复选框
            const checkboxes = document.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });

            // 重置服务项目
            this._resetServiceItems();

            // 重置总金额显示
            this._updateTotalAmount(0);

            // 更新预览
            if (typeof window.updateDocumentPreview === 'function') {
                window.updateDocumentPreview();
            }

            pageLogger.info('FormProcessor', 'resetForm', '✅ 表单重置完成');
            showSuccessNotification('表单重置', '表单已成功重置');

        } catch (error) {
            pageLogger.error('FormProcessor', 'resetForm', '❌ 表单重置失败', error);
        }
    }

    /**
     * 获取表单数据
     * @returns {Object} 表单数据
     */
    getFormData() {
        const formData = {};

        try {
            // 获取基础字段
            const basicFields = [
                'customer-name', 'channel', 'receipt-number-input', 
                'receipt-date-input', 'notes'
            ];

            basicFields.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    formData[fieldId] = element.value;
                }
            });

            // 获取买方信息
            formData.buyerInfo = this._getBuyerInfo();

            // 获取服务项目
            formData.services = this._getServiceItems();

            // 获取总金额
            const totalAmountElement = document.getElementById('total-amount-display');
            if (totalAmountElement) {
                formData.totalAmount = parseFloat(totalAmountElement.textContent) || 0;
            }

            // 获取文档类型
            formData.documentType = this._getCurrentDocumentType();

            return formData;

        } catch (error) {
            pageLogger.error('FormProcessor', 'getFormData', '获取表单数据失败', error);
            return {};
        }
    }

    // #region 私有方法
    /**
     * 初始化表单事件处理器
     * @private
     */
    _initializeFormHandlers() {
        // 添加服务项目按钮
        const addServiceBtn = document.getElementById('add-service-item');
        if (addServiceBtn) {
            addServiceBtn.addEventListener('click', () => {
                this._addServiceItem();
            });
        }

        // 监听服务项目变化
        this._setupServiceItemListeners();

        pageLogger.debug('FormProcessor', '_initializeFormHandlers', '表单事件处理器已初始化');
    }

    /**
     * 填充单个字段
     * @param {string} elementId - 元素ID
     * @param {*} value - 值
     * @private
     */
    _fillField(elementId, value) {
        const element = document.getElementById(elementId);
        if (element) {
            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                element.value = value;
            } else {
                element.textContent = value;
            }
            pageLogger.debug('FormProcessor', '_fillField', `字段已填充: ${elementId} = ${value}`);
        }
    }

    /**
     * 填充买方信息
     * @param {Object} buyerInfo - 买方信息
     * @private
     */
    _fillBuyerInfo(buyerInfo) {
        const buyerFields = {
            company: 'buyer-company',
            taxId: 'buyer-taxid',
            bank: 'buyer-bank',
            account: 'buyer-account',
            tel: 'buyer-tel'
        };

        for (const [key, elementId] of Object.entries(buyerFields)) {
            if (buyerInfo[key]) {
                this._fillField(elementId, buyerInfo[key]);
            }
        }
    }

    /**
     * 填充服务项目
     * @param {Array} services - 服务项目数组
     * @param {Object} extractedData - 完整的提取数据
     * @private
     */
    _fillServiceItems(services, extractedData) {
        const container = document.getElementById('service-items-container');
        if (!container) return;

        // 清空现有项目
        container.innerHTML = '';

        services.forEach((service, index) => {
            const serviceItem = document.createElement('div');
            serviceItem.className = 'service-item mb-2 flex items-center space-x-1';

            const quantity = 1;
            const unitPrice = extractedData.totalAmount ? 
                (extractedData.totalAmount / services.length) : 0;
            const amount = quantity * unitPrice;

            serviceItem.innerHTML = `
                <input type="text" class="service-description flex-grow bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-l-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="项目描述" value="${service}">
                <input type="number" class="service-quantity w-16 bg-gray-50 border border-gray-300 text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="数量" value="${quantity}" min="0">
                <input type="number" class="service-unit-price w-24 bg-gray-50 border border-gray-300 text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="单价" value="${unitPrice.toFixed(2)}" min="0" step="0.01">
                <input type="number" class="service-amount w-24 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-r-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="金额" value="${amount.toFixed(2)}" min="0" step="0.01">
                <button type="button" class="remove-item ml-1 text-red-500 hover:text-red-700 p-2" title="删除项目">
                    <i class="fas fa-times"></i>
                </button>
            `;

            container.appendChild(serviceItem);

            // 添加删除按钮事件
            const removeBtn = serviceItem.querySelector('.remove-item');
            if (removeBtn) {
                removeBtn.addEventListener('click', () => {
                    serviceItem.remove();
                    this._updateTotalFromServiceItems();
                });
            }
        });

        // 设置服务项目监听器
        this._setupServiceItemListeners();
    }

    /**
     * 填充文档特定字段
     * @param {Object} extractedData - 提取的数据
     * @param {string} documentType - 文档类型
     * @private
     */
    _fillDocumentSpecificFields(extractedData, documentType) {
        switch (documentType) {
            case 'invoice':
                if (extractedData.taxRate) {
                    this._fillField('tax-rate-input', extractedData.taxRate);
                }
                break;
            case 'quotation':
                if (extractedData.validUntil) {
                    this._fillField('valid-until-input', extractedData.validUntil);
                }
                if (extractedData.discount) {
                    this._fillField('discount-percentage-input', extractedData.discount);
                }
                if (extractedData.preparedBy) {
                    this._fillField('prepared-by-input', extractedData.preparedBy);
                }
                break;
        }
    }

    /**
     * 更新总金额显示
     * @param {number} amount - 指定金额（可选）
     * @private
     */
    _updateTotalAmount(amount = null) {
        const totalDisplay = document.getElementById('total-amount-display');
        if (!totalDisplay) return;

        if (amount !== null) {
            totalDisplay.textContent = amount.toFixed(2);
        } else {
            // 从服务项目计算总金额
            this._updateTotalFromServiceItems();
        }
    }

    /**
     * 从服务项目更新总金额
     * @private
     */
    _updateTotalFromServiceItems() {
        const serviceItems = this._getServiceItems();
        const total = serviceItems.reduce((sum, item) => sum + (item.amount || 0), 0);
        this._updateTotalAmount(total);
    }

    /**
     * 获取服务项目数据
     * @returns {Array} 服务项目数组
     * @private
     */
    _getServiceItems() {
        const container = document.getElementById('service-items-container');
        if (!container) return [];

        const items = [];
        const serviceElements = container.querySelectorAll('.service-item');

        serviceElements.forEach(element => {
            const description = element.querySelector('.service-description')?.value || '';
            const quantity = parseFloat(element.querySelector('.service-quantity')?.value) || 0;
            const unitPrice = parseFloat(element.querySelector('.service-unit-price')?.value) || 0;
            const amount = parseFloat(element.querySelector('.service-amount')?.value) || 0;

            if (description.trim()) {
                items.push({
                    description,
                    quantity,
                    unitPrice,
                    amount
                });
            }
        });

        return items;
    }

    /**
     * 获取买方信息
     * @returns {Object} 买方信息
     * @private
     */
    _getBuyerInfo() {
        return {
            company: document.getElementById('buyer-company')?.value || '',
            taxId: document.getElementById('buyer-taxid')?.value || '',
            bank: document.getElementById('buyer-bank')?.value || '',
            account: document.getElementById('buyer-account')?.value || '',
            tel: document.getElementById('buyer-tel')?.value || ''
        };
    }

    /**
     * 获取当前文档类型
     * @returns {string} 文档类型
     * @private
     */
    _getCurrentDocumentType() {
        const documentTypeSelect = document.getElementById('document-type');
        return documentTypeSelect?.value || 'receipt';
    }

    /**
     * 获取已填写字段数量
     * @returns {number} 已填写字段数量
     * @private
     */
    _getFilledFieldCount() {
        const inputs = document.querySelectorAll('input, textarea, select');
        let count = 0;

        inputs.forEach(input => {
            if (input.value && input.value.trim()) {
                count++;
            }
        });

        return count;
    }

    /**
     * 添加服务项目
     * @private
     */
    _addServiceItem() {
        const container = document.getElementById('service-items-container');
        if (!container) return;

        const serviceItem = document.createElement('div');
        serviceItem.className = 'service-item mb-2 flex items-center space-x-1';
        serviceItem.innerHTML = `
            <input type="text" class="service-description flex-grow bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-l-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="项目描述">
            <input type="number" class="service-quantity w-16 bg-gray-50 border border-gray-300 text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="数量" value="1" min="0">
            <input type="number" class="service-unit-price w-24 bg-gray-50 border border-gray-300 text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="单价" min="0" step="0.01">
            <input type="number" class="service-amount w-24 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-r-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="金额" min="0" step="0.01">
            <button type="button" class="remove-item ml-1 text-red-500 hover:text-red-700 p-2" title="删除项目">
                <i class="fas fa-times"></i>
            </button>
        `;

        container.appendChild(serviceItem);

        // 添加删除按钮事件
        const removeBtn = serviceItem.querySelector('.remove-item');
        if (removeBtn) {
            removeBtn.addEventListener('click', () => {
                serviceItem.remove();
                this._updateTotalFromServiceItems();
            });
        }

        // 设置新项目的监听器
        this._setupServiceItemListeners();
    }

    /**
     * 重置服务项目
     * @private
     */
    _resetServiceItems() {
        const container = document.getElementById('service-items-container');
        if (!container) return;

        container.innerHTML = `
            <div class="service-item mb-2 flex items-center space-x-1">
                <input type="text" class="service-description flex-grow bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-l-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="项目描述">
                <input type="number" class="service-quantity w-16 bg-gray-50 border border-gray-300 text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="数量" value="1" min="0">
                <input type="number" class="service-unit-price w-24 bg-gray-50 border border-gray-300 text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="单价" min="0" step="0.01">
                <input type="number" class="service-amount w-24 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-r-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="金额" min="0" step="0.01">
                <button type="button" class="remove-item ml-1 text-red-500 hover:text-red-700 p-2" title="删除项目">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        `;

        this._setupServiceItemListeners();
    }

    /**
     * 设置服务项目监听器
     * @private
     */
    _setupServiceItemListeners() {
        const container = document.getElementById('service-items-container');
        if (!container) return;

        // 为所有服务项目输入框添加变化监听器
        const inputs = container.querySelectorAll('input');
        inputs.forEach(input => {
            input.addEventListener('input', () => {
                this._updateTotalFromServiceItems();
            });
        });

        // 为删除按钮添加事件
        const removeButtons = container.querySelectorAll('.remove-item');
        removeButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.target.closest('.service-item').remove();
                this._updateTotalFromServiceItems();
            });
        });
    }
    // #endregion
}
// #endregion

// #region 导出函数
/**
 * 创建表单处理器实例
 * @returns {FormProcessor} 表单处理器实例
 */
export function createFormProcessor() {
    return new FormProcessor();
}

/**
 * 获取全局表单处理器
 * @returns {FormProcessor} 全局表单处理器实例
 */
export function getFormProcessor() {
    if (!window._globalFormProcessor) {
        window._globalFormProcessor = new FormProcessor();
    }
    return window._globalFormProcessor;
}
// #endregion
