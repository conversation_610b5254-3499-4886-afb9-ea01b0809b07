/**
 * @file Gemini AI处理器模块 - 从index.html提取的Gemini处理功能
 * @description 提供Gemini AI集成和智能分析功能
 */

import { getLogger } from '../core/logger.js';

// #region 全局变量
const pageLogger = getLogger();
// #endregion

// #region Gemini NLP处理器类
/**
 * @class GeminiNLPProcessor - Gemini AI处理器
 * @description 提供Gemini AI集成的自然语言处理功能
 */
export class GeminiNLPProcessor {
    /**
     * 构造函数 - 初始化Gemini处理器
     * @param {Object} options - 配置选项
     */
    constructor(options = {}) {
        pageLogger.info('GeminiNLPProcessor', 'constructor', '🤖 初始化Gemini处理器');
        
        this.apiKey = options.apiKey || 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s';
        this.apiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
        this.visionApiUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro-vision:generateContent';
        
        this.isInitialized = true;
        this.stats = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0
        };
        
        pageLogger.info('GeminiNLPProcessor', 'constructor', '✅ Gemini处理器初始化完成');
    }

    /**
     * 处理文本分析
     * @param {string} text - 要分析的文本
     * @param {string} documentType - 文档类型
     * @returns {Promise<Object>} 分析结果
     */
    async processText(text, documentType = 'receipt') {
        const startTime = Date.now();
        this.stats.totalRequests++;
        
        try {
            pageLogger.info('GeminiNLPProcessor', 'processText', '🧠 开始Gemini文本分析', {
                textLength: text.length,
                documentType
            });

            const prompt = this._buildPrompt(text, documentType);
            const requestBody = {
                contents: [{
                    parts: [{ text: prompt }]
                }],
                generationConfig: {
                    temperature: 0.1,
                    topK: 1,
                    topP: 1,
                    maxOutputTokens: 2048,
                }
            };

            const response = await this._makeRequest(this.apiUrl, requestBody);
            const result = this._parseResponse(response, documentType);
            
            // 更新统计
            const responseTime = Date.now() - startTime;
            this._updateStats(responseTime, true);
            
            pageLogger.info('GeminiNLPProcessor', 'processText', '✅ Gemini文本分析完成', {
                responseTime: `${responseTime}ms`,
                fieldsExtracted: Object.keys(result).length
            });

            return result;

        } catch (error) {
            this._updateStats(Date.now() - startTime, false);
            pageLogger.error('GeminiNLPProcessor', 'processText', '❌ Gemini文本分析失败', error);
            throw error;
        }
    }

    /**
     * 处理图片分析
     * @param {string} imageData - Base64图片数据
     * @param {string} documentType - 文档类型
     * @returns {Promise<Object>} 分析结果
     */
    async processImage(imageData, documentType = 'receipt') {
        const startTime = Date.now();
        this.stats.totalRequests++;
        
        try {
            pageLogger.info('GeminiNLPProcessor', 'processImage', '🖼️ 开始Gemini图片分析', {
                documentType,
                imageSize: imageData.length
            });

            const prompt = this._buildImagePrompt(documentType);
            
            const requestBody = {
                contents: [{
                    parts: [
                        { text: prompt },
                        {
                            inline_data: {
                                mime_type: "image/jpeg",
                                data: imageData.split(',')[1] // 移除data:image/jpeg;base64,前缀
                            }
                        }
                    ]
                }],
                generationConfig: {
                    temperature: 0.1,
                    topK: 1,
                    topP: 1,
                    maxOutputTokens: 2048,
                }
            };

            const response = await this._makeRequest(this.visionApiUrl, requestBody);
            const result = this._parseResponse(response, documentType);
            
            // 更新统计
            const responseTime = Date.now() - startTime;
            this._updateStats(responseTime, true);
            
            pageLogger.info('GeminiNLPProcessor', 'processImage', '✅ Gemini图片分析完成', {
                responseTime: `${responseTime}ms`,
                fieldsExtracted: Object.keys(result).length
            });

            return result;

        } catch (error) {
            this._updateStats(Date.now() - startTime, false);
            pageLogger.error('GeminiNLPProcessor', 'processImage', '❌ Gemini图片分析失败', error);
            throw error;
        }
    }

    /**
     * 获取统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            successRate: this.stats.totalRequests > 0 ? 
                (this.stats.successfulRequests / this.stats.totalRequests * 100).toFixed(2) + '%' : '0%'
        };
    }

    // #region 私有方法
    /**
     * 构建文本分析提示词
     * @param {string} text - 输入文本
     * @param {string} documentType - 文档类型
     * @returns {string} 提示词
     * @private
     */
    _buildPrompt(text, documentType) {
        const basePrompt = `请分析以下文本内容，提取相关信息并以JSON格式返回。`;
        
        const typeSpecificPrompts = {
            receipt: `这是一个收据相关的文本，请提取：客户名称、服务项目、金额、日期、渠道等信息。`,
            invoice: `这是一个发票相关的文本，请提取：客户信息、服务项目、金额、税率、日期等信息。`,
            quotation: `这是一个报价单相关的文本，请提取：客户信息、服务项目、报价金额、有效期等信息。`,
            driver_agreement: `这是一个司机协议相关的文本，请提取：司机信息、车辆信息、协议条款等信息。`
        };

        const specificPrompt = typeSpecificPrompts[documentType] || typeSpecificPrompts.receipt;
        
        return `${basePrompt}\n${specificPrompt}\n\n文本内容：\n${text}\n\n请返回JSON格式的结果，包含提取的字段和对应的值。`;
    }

    /**
     * 构建图片分析提示词
     * @param {string} documentType - 文档类型
     * @returns {string} 提示词
     * @private
     */
    _buildImagePrompt(documentType) {
        const basePrompt = `请分析这张图片中的文本内容，提取相关信息并以JSON格式返回。`;
        
        const typeSpecificPrompts = {
            receipt: `这是一张收据图片，请提取：客户名称、服务项目、金额、日期、渠道等信息。`,
            invoice: `这是一张发票图片，请提取：客户信息、服务项目、金额、税率、日期等信息。`,
            quotation: `这是一张报价单图片，请提取：客户信息、服务项目、报价金额、有效期等信息。`,
            driver_agreement: `这是一张司机协议图片，请提取：司机信息、车辆信息、协议条款等信息。`
        };

        const specificPrompt = typeSpecificPrompts[documentType] || typeSpecificPrompts.receipt;
        
        return `${basePrompt}\n${specificPrompt}\n\n请返回JSON格式的结果，包含提取的字段和对应的值。`;
    }

    /**
     * 发送API请求
     * @param {string} url - API地址
     * @param {Object} requestBody - 请求体
     * @returns {Promise<Object>} 响应结果
     * @private
     */
    async _makeRequest(url, requestBody) {
        if (!this.apiKey) {
            throw new Error('Gemini API密钥未配置');
        }
        
        const requestUrl = `${url}?key=${this.apiKey}`;
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时
        
        try {
            const response = await fetch(requestUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                
                let errorMessage = `Gemini API错误: ${response.status}`;
                
                switch (response.status) {
                    case 400:
                        errorMessage += ' - 请求参数错误';
                        break;
                    case 401:
                        errorMessage += ' - API密钥无效';
                        break;
                    case 403:
                        errorMessage += ' - 访问被拒绝';
                        break;
                    case 429:
                        errorMessage += ' - 请求频率过高';
                        break;
                    case 500:
                        errorMessage += ' - 服务器内部错误';
                        break;
                    default:
                        errorMessage += ' - 未知错误';
                }
                
                if (errorData.error?.message) {
                    errorMessage += `: ${errorData.error.message}`;
                }
                
                throw new Error(errorMessage);
            }
            
            return await response.json();
            
        } catch (error) {
            clearTimeout(timeoutId);
            
            if (error.name === 'AbortError') {
                throw new Error('Gemini API请求超时');
            }
            
            throw error;
        }
    }

    /**
     * 解析API响应
     * @param {Object} response - API响应
     * @param {string} documentType - 文档类型
     * @returns {Object} 解析结果
     * @private
     */
    _parseResponse(response, documentType) {
        try {
            if (!response.candidates || response.candidates.length === 0) {
                throw new Error('Gemini API返回空结果');
            }
            
            const content = response.candidates[0].content.parts[0].text;
            
            // 尝试解析JSON
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                throw new Error('无法从响应中提取JSON');
            }
            
            const extractedData = JSON.parse(jsonMatch[0]);
            
            // 标准化字段名
            return this._normalizeFields(extractedData, documentType);
            
        } catch (error) {
            pageLogger.error('GeminiNLPProcessor', '_parseResponse', '响应解析失败', error);
            throw new Error(`响应解析失败: ${error.message}`);
        }
    }

    /**
     * 标准化字段名
     * @param {Object} data - 原始数据
     * @param {string} documentType - 文档类型
     * @returns {Object} 标准化后的数据
     * @private
     */
    _normalizeFields(data, documentType) {
        const normalized = {};
        
        // 字段映射表
        const fieldMappings = {
            '客户名称': 'customerName',
            '客户': 'customerName',
            '姓名': 'customerName',
            '金额': 'totalAmount',
            '总金额': 'totalAmount',
            '总计': 'totalAmount',
            '日期': 'issueDate',
            '开票日期': 'issueDate',
            '渠道': 'channel',
            '来源': 'channel',
            '服务项目': 'services',
            '项目': 'services',
            '备注': 'notes',
            '说明': 'notes'
        };
        
        // 转换字段名
        for (const [key, value] of Object.entries(data)) {
            const normalizedKey = fieldMappings[key] || key;
            normalized[normalizedKey] = value;
        }
        
        return normalized;
    }

    /**
     * 更新统计信息
     * @param {number} responseTime - 响应时间
     * @param {boolean} success - 是否成功
     * @private
     */
    _updateStats(responseTime, success) {
        if (success) {
            this.stats.successfulRequests++;
        } else {
            this.stats.failedRequests++;
        }
        
        // 更新平均响应时间
        this.stats.averageResponseTime = (
            (this.stats.averageResponseTime * (this.stats.totalRequests - 1) + responseTime) / 
            this.stats.totalRequests
        );
    }
    // #endregion
}
// #endregion

// #region 导出函数
/**
 * 创建Gemini处理器实例
 * @param {Object} options - 配置选项
 * @returns {GeminiNLPProcessor} 处理器实例
 */
export function createGeminiProcessor(options = {}) {
    return new GeminiNLPProcessor(options);
}

/**
 * 获取默认Gemini处理器
 * @returns {GeminiNLPProcessor} 默认处理器实例
 */
export function getDefaultGeminiProcessor() {
    if (!window._defaultGeminiProcessor) {
        window._defaultGeminiProcessor = new GeminiNLPProcessor();
    }
    return window._defaultGeminiProcessor;
}
// #endregion
