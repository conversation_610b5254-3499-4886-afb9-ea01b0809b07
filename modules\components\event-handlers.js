/**
 * @file 事件处理器模块 - 从index.html提取的事件处理功能
 * @description 提供统一的事件处理器设置和管理功能
 */

import { getLogger } from '../core/logger.js';

// #region 全局变量
const pageLogger = getLogger();
// #endregion

// #region 通知函数
/**
 * @function showErrorNotification - 显示错误通知
 * @param {string} title - 错误标题
 * @param {string} message - 错误消息
 */
export function showErrorNotification(title, message) {
    if (pageLogger) {
        pageLogger.debug('EventHandlers', 'showErrorNotification', '显示错误通知', {
            title, message
        });
    }

    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-red-500 text-white p-4 rounded-lg shadow-lg z-50 flex items-center max-w-sm';
    notification.innerHTML = `
        <i class="fas fa-exclamation-triangle mr-2"></i>
        <div>
            <h4 class="font-bold">${title}</h4>
            <p class="text-sm">${message}</p>
        </div>
        <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    document.body.appendChild(notification);

    // 5秒后自动移除
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

/**
 * @function showSuccessNotification - 显示成功通知
 * @param {string} title - 通知标题
 * @param {string} message - 通知消息
 */
export function showSuccessNotification(title, message) {
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-green-500 text-white p-4 rounded-lg shadow-lg z-50 flex items-center max-w-sm';
    notification.innerHTML = `
        <i class="fas fa-check-circle mr-3 text-xl"></i>
        <div>
            <h4 class="font-bold">${title}</h4>
            <p class="text-sm">${message}</p>
        </div>
        <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 3000);
}

/**
 * @function showWarningNotification - 显示警告通知
 * @param {string} title - 通知标题
 * @param {string} message - 通知消息
 */
export function showWarningNotification(title, message) {
    const notification = document.createElement('div');
    notification.className = 'fixed top-4 right-4 bg-yellow-500 text-white p-4 rounded-lg shadow-lg z-50 flex items-center max-w-sm';
    notification.innerHTML = `
        <i class="fas fa-exclamation-triangle mr-3 text-xl"></i>
        <div>
            <h4 class="font-bold">${title}</h4>
            <p class="text-sm">${message}</p>
        </div>
        <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.remove()">
            <i class="fas fa-times"></i>
        </button>
    `;

    document.body.appendChild(notification);

    // 4秒后自动移除
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 4000);
}
// #endregion

// #region 事件处理器设置
/**
 * @function setupLegacyEventHandlers - 设置传统事件处理器
 * @param {SmartOfficeApp} app - SmartOffice应用实例
 */
export function setupLegacyEventHandlers(app) {
    const setupStartTime = performance.now();
    pageLogger.startPerformanceMark('event_handlers_setup', 'EventHandlers', 'setupLegacyEventHandlers');

    try {
        pageLogger.info('EventHandlers', 'setupLegacyEventHandlers', '🔧 开始设置传统事件处理器');

        // 设置公司选择器事件
        setupCompanySelector();
        
        // 设置Gemini配置事件
        setupGeminiConfigHandlers();
        
        // 设置图片上传事件
        setupImageUploadHandlers();
        
        // 设置表单事件
        setupFormHandlers();

        const setupDuration = pageLogger.endPerformanceMark('event_handlers_setup', 'EventHandlers', 'setupLegacyEventHandlers');
        pageLogger.info('EventHandlers', 'setupLegacyEventHandlers', '✅ 传统事件处理器设置完成', {
            duration: `${setupDuration?.toFixed(2)}ms`,
            handlersCount: 4
        });

    } catch (error) {
        pageLogger.error('EventHandlers', 'setupLegacyEventHandlers', '❌ 事件处理器设置失败', error);
        throw error;
    }
}

/**
 * @function setupCompanySelector - 设置公司选择器事件
 */
function setupCompanySelector() {
    const companySelector = document.getElementById('company-selector');
    if (companySelector) {
        companySelector.addEventListener('change', (e) => {
            const companyCode = e.target.value;
            if (window.smartOfficeApp && typeof window.smartOfficeApp.updateCompanyImages === 'function') {
                window.smartOfficeApp.updateCompanyImages(companyCode);
            } else if (typeof updateCompanyImages === 'function') {
                updateCompanyImages(companyCode);
            }
        });
        pageLogger.debug('EventHandlers', 'setupCompanySelector', '公司选择器事件已设置');
    }
}

/**
 * @function setupGeminiConfigHandlers - 设置Gemini配置事件处理器
 */
function setupGeminiConfigHandlers() {
    pageLogger.debug('EventHandlers', 'setupGeminiConfigHandlers', '开始设置Gemini配置事件处理器');
    
    const nlpModeSelector = document.getElementById('nlp-mode-selector');
    if (nlpModeSelector) {
        nlpModeSelector.addEventListener('change', updateNLPConfig);
        pageLogger.debug('EventHandlers', 'setupGeminiConfigHandlers', 'NLP模式选择器事件已设置');
    }
}

/**
 * @function setupImageUploadHandlers - 设置图片上传事件处理器
 */
function setupImageUploadHandlers() {
    pageLogger.debug('EventHandlers', 'setupImageUploadHandlers', '开始设置图片上传事件处理器');
    
    const imageUploadInput = document.getElementById('image-upload-input');
    if (imageUploadInput) {
        imageUploadInput.addEventListener('change', handleImageUpload);
        pageLogger.debug('EventHandlers', 'setupImageUploadHandlers', '图片上传事件已设置');
    }
}

/**
 * @function setupFormHandlers - 设置表单事件处理器
 */
function setupFormHandlers() {
    pageLogger.debug('EventHandlers', 'setupFormHandlers', '开始设置表单事件处理器');
    
    // 解析按钮事件
    const parseButton = document.getElementById('parse-button');
    if (parseButton) {
        parseButton.addEventListener('click', handleParseButtonClick);
    }
    
    // 更新预览按钮事件
    const updatePreviewButton = document.getElementById('update-preview-button');
    if (updatePreviewButton) {
        updatePreviewButton.addEventListener('click', handleUpdatePreview);
    }
    
    // 重置表单按钮事件
    const resetFormButton = document.getElementById('reset-form-button');
    if (resetFormButton) {
        resetFormButton.addEventListener('click', handleResetForm);
    }
    
    pageLogger.debug('EventHandlers', 'setupFormHandlers', '表单事件处理器设置完成');
}
// #endregion

// #region 事件处理函数
/**
 * @function handleImageUpload - 处理图片上传
 * @param {Event} event - 上传事件
 */
function handleImageUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    pageLogger.info('EventHandlers', 'handleImageUpload', '处理图片上传', {
        fileName: file.name,
        fileSize: file.size,
        fileType: file.type
    });

    // 显示图片预览
    const reader = new FileReader();
    reader.onload = function(e) {
        const previewContainer = document.getElementById('image-preview-container');
        const previewImage = document.getElementById('image-preview');
        const previewPlaceholder = document.getElementById('image-preview-placeholder');
        const filenameSpan = document.getElementById('image-upload-filename');

        if (previewContainer && previewImage && previewPlaceholder && filenameSpan) {
            previewContainer.classList.remove('hidden');
            previewImage.src = e.target.result;
            previewImage.classList.remove('hidden');
            previewPlaceholder.classList.add('hidden');
            filenameSpan.textContent = file.name;
        }
    };
    reader.readAsDataURL(file);
}

/**
 * @function handleParseButtonClick - 处理解析按钮点击
 * @param {Event} event - 点击事件
 */
function handleParseButtonClick(event) {
    pageLogger.info('EventHandlers', 'handleParseButtonClick', '解析按钮被点击');
    
    const textInput = document.getElementById('natural-language-input');
    const text = textInput?.value?.trim();
    
    if (!text) {
        showWarningNotification('输入为空', '请输入要解析的文本内容');
        return;
    }
    
    // 调用NLP处理
    if (window.smartOfficeApp && typeof window.smartOfficeApp.processNLP === 'function') {
        window.smartOfficeApp.processNLP(text);
    }
}

/**
 * @function handleUpdatePreview - 处理更新预览
 * @param {Event} event - 点击事件
 */
function handleUpdatePreview(event) {
    pageLogger.info('EventHandlers', 'handleUpdatePreview', '更新预览按钮被点击');
    
    if (typeof window.updateDocumentPreview === 'function') {
        window.updateDocumentPreview();
    }
}

/**
 * @function handleResetForm - 处理重置表单
 * @param {Event} event - 点击事件
 */
function handleResetForm(event) {
    pageLogger.info('EventHandlers', 'handleResetForm', '重置表单按钮被点击');
    
    if (confirm('确定要重置表单吗？这将清除所有已填写的内容。')) {
        // 重置表单逻辑
        const form = document.querySelector('form') || document.body;
        const inputs = form.querySelectorAll('input, textarea, select');
        
        inputs.forEach(input => {
            if (input.type === 'checkbox' || input.type === 'radio') {
                input.checked = false;
            } else {
                input.value = '';
            }
        });
        
        showSuccessNotification('表单重置', '表单已成功重置');
    }
}
// #endregion

// #region 辅助函数
/**
 * @function updateNLPConfig - 更新NLP配置
 */
function updateNLPConfig() {
    const mode = document.getElementById('nlp-mode-selector')?.value;
    const apiKey = 'AIzaSyDEycmjd2in4sexl61jnpysIJ4nzdeDa3s'; // 硬植入的API密钥
    
    const config = {
        mode: mode || 'gemini',
        apiKey: apiKey,
        endpoint: 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent'
    };
    
    pageLogger.info('EventHandlers', 'updateNLPConfig', 'NLP配置已更新', { mode });
    
    // 更新全局配置
    if (window.smartOfficeApp && typeof window.smartOfficeApp.updateNLPConfig === 'function') {
        window.smartOfficeApp.updateNLPConfig(config);
    }
}
// #endregion
