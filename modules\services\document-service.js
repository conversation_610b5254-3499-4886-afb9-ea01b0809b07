/**
 * @file 文档服务 - 统一文档处理和管理服务模块
 * <AUTHOR> Team
 * @description 
 * 统一的文档服务，整合了原js/services/document-service.js的功能
 * 负责文档的创建、编辑、验证和管理
 * 
 * 功能特性：
 * - 文档模型管理
 * - 模板处理和渲染
 * - 文档验证和格式化
 * - 文档历史和版本控制
 * - 自动保存和恢复
 * - 智能填充集成
 */

// #region 导入依赖
import { getLogger } from '../core/logger.js';
import { getEventBus } from '../core/events.js';
// #endregion

// #region 文档服务类定义
/**
 * @class DocumentService - 统一文档处理服务类
 * @description 提供文档创建、编辑、验证等核心功能
 */
export class DocumentService {
    /**
     * 构造函数 - 初始化文档服务
     * @param {Object} options - 服务配置选项
     */
    constructor(options = {}) {
        this.logger = getLogger();
        this.eventBus = getEventBus();
        
        // 基础配置
        this.config = options.config;
        this.nlpService = options.nlpService;
        
        // 文档配置
        this.documentConfig = {
            defaultType: 'receipt',
            autoSave: true,
            saveInterval: 30000, // 30秒自动保存
            maxHistory: 50,
            enableValidation: true,
            enableVersionControl: true,
            ...options.documentConfig
        };
        
        // 服务状态
        this.isInitialized = false;
        this.currentDocument = null;
        this.documentHistory = [];
        
        // 文档存储
        this.documents = new Map();
        this.templates = new Map();
        
        // 自动保存定时器
        this.autoSaveTimer = null;
        
        // 统计信息
        this.stats = {
            documentsCreated: 0,
            documentsEdited: 0,
            documentsSaved: 0,
            validationErrors: 0,
            averageEditTime: 0
        };
        
        this.logger.info('DocumentService', 'constructor', '文档服务已创建');
    }

    /**
     * 初始化文档服务
     * @returns {Promise<void>}
     */
    async initialize() {
        try {
            this.logger.info('DocumentService', 'initialize', '初始化文档服务...');
            
            // 加载模板
            await this._loadTemplates();
            
            // 恢复文档状态
            await this._restoreDocumentState();
            
            // 设置自动保存
            if (this.documentConfig.autoSave) {
                this._setupAutoSave();
            }
            
            // 设置事件监听
            this._setupEventListeners();
            
            this.isInitialized = true;
            
            // 触发初始化完成事件
            this.eventBus.emit('document:initialized', {
                service: 'DocumentService',
                timestamp: new Date()
            });
            
            this.logger.info('DocumentService', 'initialize', '文档服务初始化完成');
            
        } catch (error) {
            this.logger.error('DocumentService', 'initialize', '文档服务初始化失败', error);
            throw error;
        }
    }

    /**
     * 创建新文档
     * @param {string} type - 文档类型
     * @param {Object} data - 初始数据
     * @returns {Promise<Object>} 创建的文档对象
     */
    async createDocument(type, data = {}) {
        try {
            this.logger.info('DocumentService', 'createDocument', `创建新文档: ${type}`);
            
            // 获取模板
            const template = this.templates.get(type);
            if (!template) {
                throw new Error(`未找到文档类型 ${type} 的模板`);
            }
            
            // 创建文档对象
            const document = {
                id: this._generateDocumentId(),
                type: type,
                template: template,
                data: { ...template.defaultData, ...data },
                metadata: {
                    created: new Date(),
                    modified: new Date(),
                    version: 1,
                    author: 'SmartOffice User'
                },
                status: 'draft',
                validation: {
                    isValid: false,
                    errors: [],
                    warnings: []
                }
            };
            
            // 验证文档
            await this._validateDocument(document);
            
            // 保存文档
            this.documents.set(document.id, document);
            this.currentDocument = document;
            
            // 添加到历史记录
            this._addToHistory(document);
            
            // 更新统计
            this.stats.documentsCreated++;
            
            // 触发事件
            this.eventBus.emit('document:created', {
                documentId: document.id,
                type: type,
                timestamp: new Date()
            });
            
            this.logger.info('DocumentService', 'createDocument', `文档创建完成: ${document.id}`);
            return document;
            
        } catch (error) {
            this.logger.error('DocumentService', 'createDocument', '文档创建失败', error);
            throw error;
        }
    }

    /**
     * 更新文档数据
     * @param {string} documentId - 文档ID
     * @param {Object} updates - 更新数据
     * @returns {Promise<Object>} 更新后的文档对象
     */
    async updateDocument(documentId, updates) {
        try {
            const document = this.documents.get(documentId);
            if (!document) {
                throw new Error(`未找到文档: ${documentId}`);
            }
            
            this.logger.info('DocumentService', 'updateDocument', `更新文档: ${documentId}`);
            
            // 备份当前版本
            const backup = JSON.parse(JSON.stringify(document));
            
            // 应用更新
            document.data = { ...document.data, ...updates };
            document.metadata.modified = new Date();
            document.metadata.version++;
            
            // 验证更新后的文档
            await this._validateDocument(document);
            
            // 如果验证失败且是关键错误，回滚更改
            if (!document.validation.isValid && this._hasCriticalErrors(document.validation.errors)) {
                this.documents.set(documentId, backup);
                throw new Error('文档更新失败：存在关键验证错误');
            }
            
            // 更新统计
            this.stats.documentsEdited++;
            
            // 触发事件
            this.eventBus.emit('document:updated', {
                documentId: documentId,
                updates: updates,
                timestamp: new Date()
            });
            
            this.logger.info('DocumentService', 'updateDocument', `文档更新完成: ${documentId}`);
            return document;
            
        } catch (error) {
            this.logger.error('DocumentService', 'updateDocument', '文档更新失败', error);
            throw error;
        }
    }

    /**
     * 获取文档
     * @param {string} documentId - 文档ID
     * @returns {Object|null} 文档对象
     */
    getDocument(documentId) {
        return this.documents.get(documentId) || null;
    }

    /**
     * 获取当前文档
     * @returns {Object|null} 当前文档对象
     */
    getCurrentDocument() {
        return this.currentDocument;
    }

    /**
     * 设置当前文档
     * @param {string} documentId - 文档ID
     * @returns {Object} 设置的文档对象
     */
    setCurrentDocument(documentId) {
        const document = this.documents.get(documentId);
        if (!document) {
            throw new Error(`未找到文档: ${documentId}`);
        }
        
        this.currentDocument = document;
        
        // 触发事件
        this.eventBus.emit('document:current-changed', {
            documentId: documentId,
            timestamp: new Date()
        });
        
        return document;
    }

    /**
     * 保存文档
     * @param {string} documentId - 文档ID
     * @returns {Promise<boolean>} 保存是否成功
     */
    async saveDocument(documentId) {
        try {
            const document = this.documents.get(documentId);
            if (!document) {
                throw new Error(`未找到文档: ${documentId}`);
            }

            this.logger.info('DocumentService', 'saveDocument', `保存文档: ${documentId}`);

            // 验证文档
            await this._validateDocument(document);

            // 保存到localStorage
            await this._saveToStorage(document);

            // 更新状态
            document.status = 'saved';
            document.metadata.saved = new Date();

            // 更新统计
            this.stats.documentsSaved++;

            // 触发事件
            this.eventBus.emit('document:saved', {
                documentId: documentId,
                timestamp: new Date()
            });

            this.logger.info('DocumentService', 'saveDocument', `文档保存完成: ${documentId}`);
            return true;

        } catch (error) {
            this.logger.error('DocumentService', 'saveDocument', '文档保存失败', error);
            return false;
        }
    }

    /**
     * 删除文档
     * @param {string} documentId - 文档ID
     * @returns {boolean} 删除是否成功
     */
    deleteDocument(documentId) {
        try {
            const document = this.documents.get(documentId);
            if (!document) {
                return false;
            }

            this.logger.info('DocumentService', 'deleteDocument', `删除文档: ${documentId}`);

            // 从存储中删除
            this.documents.delete(documentId);

            // 如果是当前文档，清除当前文档
            if (this.currentDocument && this.currentDocument.id === documentId) {
                this.currentDocument = null;
            }

            // 从历史记录中移除
            this.documentHistory = this.documentHistory.filter(doc => doc.id !== documentId);

            // 从localStorage中删除
            this._removeFromStorage(documentId);

            // 触发事件
            this.eventBus.emit('document:deleted', {
                documentId: documentId,
                timestamp: new Date()
            });

            this.logger.info('DocumentService', 'deleteDocument', `文档删除完成: ${documentId}`);
            return true;

        } catch (error) {
            this.logger.error('DocumentService', 'deleteDocument', '文档删除失败', error);
            return false;
        }
    }

    /**
     * 获取文档列表
     * @param {Object} filters - 过滤条件
     * @returns {Array} 文档列表
     */
    getDocuments(filters = {}) {
        let documents = Array.from(this.documents.values());

        // 应用过滤条件
        if (filters.type) {
            documents = documents.filter(doc => doc.type === filters.type);
        }

        if (filters.status) {
            documents = documents.filter(doc => doc.status === filters.status);
        }

        if (filters.dateFrom) {
            documents = documents.filter(doc => doc.metadata.created >= filters.dateFrom);
        }

        if (filters.dateTo) {
            documents = documents.filter(doc => doc.metadata.created <= filters.dateTo);
        }

        // 排序
        documents.sort((a, b) => b.metadata.modified - a.metadata.modified);

        return documents;
    }

    /**
     * 智能填充文档 - 使用NLP服务智能填充文档字段
     * @param {string} documentId - 文档ID
     * @param {string} text - 输入文本
     * @returns {Promise<Object>} 填充结果
     */
    async smartFillDocument(documentId, text) {
        if (!this.nlpService) {
            throw new Error('NLP服务未配置');
        }

        try {
            const document = this.documents.get(documentId);
            if (!document) {
                throw new Error(`未找到文档: ${documentId}`);
            }

            this.logger.info('DocumentService', 'smartFillDocument', `智能填充文档: ${documentId}`);

            // 获取表单结构
            const formSchema = document.template.formSchema || {};

            // 使用NLP服务进行智能填充
            const fillResult = await this.nlpService.smartFormFill(text, formSchema);

            // 应用填充结果
            if (fillResult.formData && Object.keys(fillResult.formData).length > 0) {
                await this.updateDocument(documentId, fillResult.formData);
            }

            this.logger.info('DocumentService', 'smartFillDocument', `智能填充完成: ${documentId}`);
            return fillResult;

        } catch (error) {
            this.logger.error('DocumentService', 'smartFillDocument', '智能填充失败', error);
            throw error;
        }
    }

    /**
     * 获取服务统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            ...this.stats,
            totalDocuments: this.documents.size,
            currentDocument: this.currentDocument?.id || null,
            historySize: this.documentHistory.length,
            templatesLoaded: this.templates.size,
            isInitialized: this.isInitialized
        };
    }

    // #region 私有方法
    /**
     * 加载模板
     * @private
     */
    async _loadTemplates() {
        // 默认模板定义
        const defaultTemplates = {
            receipt: {
                name: '收据',
                defaultData: {
                    receiptNumber: '',
                    date: new Date().toISOString().split('T')[0],
                    customerName: '',
                    amount: 0,
                    description: ''
                },
                formSchema: {
                    receiptNumber: { type: 'text', required: true },
                    date: { type: 'date', required: true },
                    customerName: { type: 'text', required: true },
                    amount: { type: 'number', required: true },
                    description: { type: 'text', required: false }
                }
            },
            invoice: {
                name: '发票',
                defaultData: {
                    invoiceNumber: '',
                    date: new Date().toISOString().split('T')[0],
                    customerName: '',
                    items: [],
                    total: 0
                },
                formSchema: {
                    invoiceNumber: { type: 'text', required: true },
                    date: { type: 'date', required: true },
                    customerName: { type: 'text', required: true },
                    total: { type: 'number', required: true }
                }
            },
            quotation: {
                name: '报价单',
                defaultData: {
                    quotationNumber: '',
                    date: new Date().toISOString().split('T')[0],
                    customerName: '',
                    items: [],
                    total: 0,
                    validUntil: ''
                },
                formSchema: {
                    quotationNumber: { type: 'text', required: true },
                    date: { type: 'date', required: true },
                    customerName: { type: 'text', required: true },
                    total: { type: 'number', required: true },
                    validUntil: { type: 'date', required: false }
                }
            },
            driver_agreement: {
                name: '司机协议',
                defaultData: {
                    agreementNumber: '',
                    driverName: '',
                    driverPhone: '',
                    vehicleInfo: '',
                    startDate: new Date().toISOString().split('T')[0]
                },
                formSchema: {
                    agreementNumber: { type: 'text', required: true },
                    driverName: { type: 'text', required: true },
                    driverPhone: { type: 'phone', required: true },
                    vehicleInfo: { type: 'text', required: true },
                    startDate: { type: 'date', required: true }
                }
            }
        };

        for (const [type, template] of Object.entries(defaultTemplates)) {
            this.templates.set(type, template);
        }

        this.logger.info('DocumentService', '_loadTemplates', '模板加载完成');
    }

    /**
     * 恢复文档状态
     * @private
     */
    async _restoreDocumentState() {
        try {
            const savedState = localStorage.getItem('smartoffice-documents');
            if (savedState) {
                const state = JSON.parse(savedState);

                // 恢复文档
                for (const doc of state.documents || []) {
                    this.documents.set(doc.id, doc);
                }

                // 恢复当前文档
                if (state.currentDocumentId) {
                    this.currentDocument = this.documents.get(state.currentDocumentId);
                }

                this.logger.info('DocumentService', '_restoreDocumentState', '文档状态已恢复');
            }
        } catch (error) {
            this.logger.warn('DocumentService', '_restoreDocumentState', '文档状态恢复失败', error);
        }
    }

    /**
     * 设置自动保存
     * @private
     */
    _setupAutoSave() {
        this.autoSaveTimer = setInterval(() => {
            if (this.currentDocument && this.currentDocument.status === 'draft') {
                this.saveDocument(this.currentDocument.id);
            }
        }, this.documentConfig.saveInterval);

        this.logger.info('DocumentService', '_setupAutoSave', '自动保存已启用');
    }

    /**
     * 设置事件监听
     * @private
     */
    _setupEventListeners() {
        // 监听配置变更
        this.eventBus.on('config:changed', (event) => {
            if (event.path.startsWith('document.')) {
                this._handleConfigChange(event);
            }
        });

        // 监听窗口关闭事件
        window.addEventListener('beforeunload', () => {
            this._saveCurrentState();
        });
    }

    /**
     * 验证文档
     * @param {Object} document - 文档对象
     * @private
     */
    async _validateDocument(document) {
        const errors = [];
        const warnings = [];

        // 基础验证
        if (!document.data) {
            errors.push('文档数据不能为空');
        }

        // 模板字段验证
        const schema = document.template.formSchema || {};
        for (const [fieldName, fieldConfig] of Object.entries(schema)) {
            const value = document.data[fieldName];

            // 必填字段验证
            if (fieldConfig.required && (!value || value.toString().trim() === '')) {
                errors.push(`字段 ${fieldName} 是必填的`);
            }

            // 类型验证
            if (value && fieldConfig.type) {
                const validation = this._validateFieldType(value, fieldConfig.type);
                if (!validation.isValid) {
                    errors.push(`字段 ${fieldName} 格式不正确: ${validation.message}`);
                }
            }
        }

        // 更新验证结果
        document.validation = {
            isValid: errors.length === 0,
            errors,
            warnings,
            validatedAt: new Date()
        };

        // 更新统计
        if (errors.length > 0) {
            this.stats.validationErrors++;
        }
    }

    /**
     * 验证字段类型
     * @param {*} value - 字段值
     * @param {string} type - 字段类型
     * @returns {Object} 验证结果
     * @private
     */
    _validateFieldType(value, type) {
        switch (type) {
            case 'email':
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                return {
                    isValid: emailRegex.test(value),
                    message: '邮箱格式不正确'
                };
            case 'phone':
                const phoneRegex = /^1[3-9]\d{9}$/;
                return {
                    isValid: phoneRegex.test(value.replace(/[-\s]/g, '')),
                    message: '手机号格式不正确'
                };
            case 'number':
                return {
                    isValid: !isNaN(Number(value)),
                    message: '必须是数字'
                };
            case 'date':
                return {
                    isValid: !isNaN(Date.parse(value)),
                    message: '日期格式不正确'
                };
            default:
                return { isValid: true, message: '' };
        }
    }

    /**
     * 生成文档ID
     * @returns {string} 文档ID
     * @private
     */
    _generateDocumentId() {
        return `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 添加到历史记录
     * @param {Object} document - 文档对象
     * @private
     */
    _addToHistory(document) {
        this.documentHistory.unshift(document);

        // 限制历史记录数量
        if (this.documentHistory.length > this.documentConfig.maxHistory) {
            this.documentHistory = this.documentHistory.slice(0, this.documentConfig.maxHistory);
        }
    }

    /**
     * 检查是否有关键错误
     * @param {Array} errors - 错误列表
     * @returns {boolean} 是否有关键错误
     * @private
     */
    _hasCriticalErrors(errors) {
        const criticalKeywords = ['必填', '格式不正确', '不能为空'];
        return errors.some(error =>
            criticalKeywords.some(keyword => error.includes(keyword))
        );
    }

    /**
     * 保存到存储
     * @param {Object} document - 文档对象
     * @private
     */
    async _saveToStorage(document) {
        try {
            const key = `smartoffice-doc-${document.id}`;
            localStorage.setItem(key, JSON.stringify(document));
        } catch (error) {
            this.logger.warn('DocumentService', '_saveToStorage', '保存到localStorage失败', error);
        }
    }

    /**
     * 从存储中移除
     * @param {string} documentId - 文档ID
     * @private
     */
    _removeFromStorage(documentId) {
        try {
            const key = `smartoffice-doc-${documentId}`;
            localStorage.removeItem(key);
        } catch (error) {
            this.logger.warn('DocumentService', '_removeFromStorage', '从localStorage删除失败', error);
        }
    }

    /**
     * 保存当前状态
     * @private
     */
    _saveCurrentState() {
        try {
            const state = {
                documents: Array.from(this.documents.values()),
                currentDocumentId: this.currentDocument?.id || null,
                timestamp: new Date()
            };

            localStorage.setItem('smartoffice-documents', JSON.stringify(state));
        } catch (error) {
            this.logger.warn('DocumentService', '_saveCurrentState', '保存当前状态失败', error);
        }
    }

    /**
     * 处理配置变更
     * @param {Object} event - 配置变更事件
     * @private
     */
    _handleConfigChange(event) {
        this.logger.info('DocumentService', '_handleConfigChange', `配置已变更: ${event.path}`);

        // 处理自动保存配置变更
        if (event.path === 'document.autoSave') {
            if (event.newValue && !this.autoSaveTimer) {
                this._setupAutoSave();
            } else if (!event.newValue && this.autoSaveTimer) {
                clearInterval(this.autoSaveTimer);
                this.autoSaveTimer = null;
            }
        }
    }
    // #endregion

    /**
     * 销毁文档服务
     */
    destroy() {
        // 保存当前状态
        this._saveCurrentState();

        // 清理定时器
        if (this.autoSaveTimer) {
            clearInterval(this.autoSaveTimer);
        }

        // 清理数据
        this.documents.clear();
        this.templates.clear();
        this.documentHistory = [];
        this.currentDocument = null;
        this.isInitialized = false;

        this.logger.info('DocumentService', 'destroy', '文档服务已销毁');
    }
}

// #region 导出
export default DocumentService;

// 便捷函数
export function createDocumentService(options = {}) {
    return new DocumentService(options);
}
// #endregion
