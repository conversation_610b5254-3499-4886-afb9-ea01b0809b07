/**
 * @file 通知管理器 - 统一的通知显示系统
 * 提供成功、警告、错误等各种类型的通知显示功能
 */

(function() {
    'use strict';

    /**
     * 通知管理器类
     * @class NotificationManager
     */
    class NotificationManager {
        constructor() {
            this.notifications = new Set();
            this.defaultDuration = 5000;
            this.maxNotifications = 5;
        }

        /**
         * 显示成功通知
         * @function showSuccessNotification
         * @param {string} title - 通知标题
         * @param {string} message - 通知消息
         * @param {number} duration - 显示时长（毫秒）
         */
        showSuccessNotification(title, message, duration = this.defaultDuration) {
            return this._createNotification('success', title, message, duration);
        }

        /**
         * 显示警告通知
         * @function showWarningNotification
         * @param {string} title - 通知标题
         * @param {string} message - 通知消息
         * @param {number} duration - 显示时长（毫秒）
         */
        showWarningNotification(title, message, duration = 6000) {
            return this._createNotification('warning', title, message, duration);
        }

        /**
         * 显示错误通知
         * @function showErrorNotification
         * @param {string} title - 通知标题
         * @param {string} message - 通知消息
         * @param {number} duration - 显示时长（毫秒）
         */
        showErrorNotification(title, message, duration = 8000) {
            return this._createNotification('error', title, message, duration);
        }

        /**
         * 显示信息通知
         * @function showInfoNotification
         * @param {string} title - 通知标题
         * @param {string} message - 通知消息
         * @param {number} duration - 显示时长（毫秒）
         */
        showInfoNotification(title, message, duration = this.defaultDuration) {
            return this._createNotification('info', title, message, duration);
        }

        /**
         * 创建通知元素
         * @function _createNotification
         * @param {string} type - 通知类型
         * @param {string} title - 通知标题
         * @param {string} message - 通知消息
         * @param {number} duration - 显示时长
         * @returns {HTMLElement} 通知元素
         * @private
         */
        _createNotification(type, title, message, duration) {
            // 检查通知数量限制
            if (this.notifications.size >= this.maxNotifications) {
                this._removeOldestNotification();
            }

            const notification = document.createElement('div');
            const notificationId = `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
            notification.id = notificationId;

            // 设置通知样式类
            const typeStyles = this._getTypeStyles(type);
            notification.className = `fixed top-4 right-4 ${typeStyles.bg} text-white p-4 rounded-lg shadow-lg z-50 flex items-center max-w-sm notification-item`;

            // 设置通知内容
            notification.innerHTML = `
                <i class="${typeStyles.icon} mr-3 text-xl"></i>
                <div class="flex-grow">
                    <h4 class="font-bold">${this._escapeHtml(title)}</h4>
                    <p class="text-sm">${this._escapeHtml(message)}</p>
                </div>
                <button class="ml-4 text-white hover:text-gray-200 notification-close" title="关闭通知">
                    <i class="fas fa-times"></i>
                </button>
            `;

            // 添加关闭事件
            const closeBtn = notification.querySelector('.notification-close');
            closeBtn.addEventListener('click', () => {
                this._removeNotification(notification);
            });

            // 添加到页面
            document.body.appendChild(notification);
            this.notifications.add(notification);

            // 添加进入动画
            requestAnimationFrame(() => {
                notification.style.transform = 'translateX(0)';
                notification.style.opacity = '1';
            });

            // 设置自动移除
            if (duration > 0) {
                setTimeout(() => {
                    this._removeNotification(notification);
                }, duration);
            }

            // 记录日志
            if (window.SmartOffice && window.SmartOffice.Logger) {
                window.SmartOffice.Logger.trace('NotificationManager', '_createNotification', `${type}通知已显示`, {
                    title, message, duration, notificationId
                });
            }

            return notification;
        }

        /**
         * 获取通知类型样式
         * @function _getTypeStyles
         * @param {string} type - 通知类型
         * @returns {Object} 样式配置
         * @private
         */
        _getTypeStyles(type) {
            const styles = {
                success: {
                    bg: 'bg-green-500',
                    icon: 'fas fa-check-circle'
                },
                warning: {
                    bg: 'bg-yellow-500',
                    icon: 'fas fa-exclamation-triangle'
                },
                error: {
                    bg: 'bg-red-500',
                    icon: 'fas fa-exclamation-circle'
                },
                info: {
                    bg: 'bg-blue-500',
                    icon: 'fas fa-info-circle'
                }
            };

            return styles[type] || styles.info;
        }

        /**
         * 移除通知
         * @function _removeNotification
         * @param {HTMLElement} notification - 通知元素
         * @private
         */
        _removeNotification(notification) {
            if (!notification || !notification.parentElement) return;

            // 添加退出动画
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';

            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
                this.notifications.delete(notification);
            }, 300);
        }

        /**
         * 移除最旧的通知
         * @function _removeOldestNotification
         * @private
         */
        _removeOldestNotification() {
            const oldest = this.notifications.values().next().value;
            if (oldest) {
                this._removeNotification(oldest);
            }
        }

        /**
         * 清除所有通知
         * @function clearAllNotifications
         */
        clearAllNotifications() {
            const notifications = Array.from(this.notifications);
            notifications.forEach(notification => {
                this._removeNotification(notification);
            });
        }

        /**
         * HTML转义
         * @function _escapeHtml
         * @param {string} text - 要转义的文本
         * @returns {string} 转义后的文本
         * @private
         */
        _escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        /**
         * 清理资源
         * @function cleanup
         */
        cleanup() {
            this.clearAllNotifications();
            this.notifications.clear();
        }
    }

    // 创建全局实例
    const notificationManager = new NotificationManager();

    // 添加CSS样式
    const style = document.createElement('style');
    style.textContent = `
        .notification-item {
            transition: all 0.3s ease-in-out;
            transform: translateX(100%);
            opacity: 0;
        }
        
        .notification-item:hover {
            transform: translateX(-5px) !important;
        }
    `;
    document.head.appendChild(style);

    // 导出到全局命名空间
    if (!window.SmartOffice) {
        window.SmartOffice = {};
    }
    if (!window.SmartOffice.Components) {
        window.SmartOffice.Components = {};
    }

    window.SmartOffice.Components.NotificationManager = NotificationManager;
    window.SmartOffice.Components.notificationManager = notificationManager;

    // 兼容性：导出全局函数
    window.showSuccessNotification = notificationManager.showSuccessNotification.bind(notificationManager);
    window.showWarningNotification = notificationManager.showWarningNotification.bind(notificationManager);
    window.showErrorNotification = notificationManager.showErrorNotification.bind(notificationManager);
    window.showInfoNotification = notificationManager.showInfoNotification.bind(notificationManager);

})();
