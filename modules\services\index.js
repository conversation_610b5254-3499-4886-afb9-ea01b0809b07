/**
 * @file 服务模块索引 - 统一服务模块导出
 * <AUTHOR> Team
 * @description 
 * 统一的服务模块索引文件，提供所有服务的导出和便捷访问
 * 整合了原js/services/目录中的所有服务功能
 * 
 * 功能特性：
 * - 统一服务导出接口
 * - 服务工厂函数
 * - 服务依赖管理
 * - 服务生命周期管理
 * - 服务注册和发现
 */

// #region 导入服务模块
import DocumentService, { createDocumentService } from './document-service.js';
import ExportService, { createExportService } from './export-service.js';
import NLPService, { createNLPService } from './nlp-service.js';
import AppInitializer, { createAppInitializer } from './app-initializer.js';
// #endregion

// #region 服务管理器
/**
 * @class ServiceManager - 服务管理器类
 * @description 管理所有服务的生命周期和依赖关系
 */
export class ServiceManager {
    constructor() {
        this.services = new Map();
        this.serviceConfigs = new Map();
        this.isInitialized = false;
        
        console.log('[ServiceManager] 服务管理器已创建');
    }

    /**
     * 注册服务
     * @param {string} name - 服务名称
     * @param {Function} serviceClass - 服务类
     * @param {Object} config - 服务配置
     */
    register(name, serviceClass, config = {}) {
        this.serviceConfigs.set(name, { serviceClass, config });
        console.log(`[ServiceManager] 服务已注册: ${name}`);
    }

    /**
     * 创建服务实例
     * @param {string} name - 服务名称
     * @param {Object} options - 创建选项
     * @returns {Object} 服务实例
     */
    create(name, options = {}) {
        const serviceConfig = this.serviceConfigs.get(name);
        if (!serviceConfig) {
            throw new Error(`未找到服务: ${name}`);
        }

        const { serviceClass, config } = serviceConfig;
        const mergedConfig = { ...config, ...options };
        
        const service = new serviceClass(mergedConfig);
        this.services.set(name, service);
        
        console.log(`[ServiceManager] 服务实例已创建: ${name}`);
        return service;
    }

    /**
     * 获取服务实例
     * @param {string} name - 服务名称
     * @returns {Object|null} 服务实例
     */
    get(name) {
        return this.services.get(name) || null;
    }

    /**
     * 初始化所有服务
     * @param {Object} globalConfig - 全局配置
     * @returns {Promise<void>}
     */
    async initializeAll(globalConfig = {}) {
        console.log('[ServiceManager] 开始初始化所有服务...');
        
        const initPromises = [];
        
        for (const [name, service] of this.services) {
            if (service.initialize && typeof service.initialize === 'function') {
                initPromises.push(
                    service.initialize().catch(error => {
                        console.error(`[ServiceManager] 服务初始化失败: ${name}`, error);
                        return { name, error };
                    })
                );
            }
        }
        
        const results = await Promise.all(initPromises);
        const failures = results.filter(result => result && result.error);
        
        if (failures.length > 0) {
            console.warn('[ServiceManager] 部分服务初始化失败:', failures);
        }
        
        this.isInitialized = true;
        console.log('[ServiceManager] 所有服务初始化完成');
    }

    /**
     * 销毁所有服务
     */
    destroyAll() {
        console.log('[ServiceManager] 开始销毁所有服务...');
        
        for (const [name, service] of this.services) {
            if (service.destroy && typeof service.destroy === 'function') {
                try {
                    service.destroy();
                    console.log(`[ServiceManager] 服务已销毁: ${name}`);
                } catch (error) {
                    console.error(`[ServiceManager] 服务销毁失败: ${name}`, error);
                }
            }
        }
        
        this.services.clear();
        this.isInitialized = false;
        console.log('[ServiceManager] 所有服务已销毁');
    }

    /**
     * 获取服务状态
     * @returns {Object} 服务状态信息
     */
    getStatus() {
        const status = {
            isInitialized: this.isInitialized,
            totalServices: this.services.size,
            registeredServices: this.serviceConfigs.size,
            services: {}
        };

        for (const [name, service] of this.services) {
            status.services[name] = {
                isInitialized: service.isInitialized || false,
                hasStats: typeof service.getStats === 'function'
            };

            if (status.services[name].hasStats) {
                try {
                    status.services[name].stats = service.getStats();
                } catch (error) {
                    status.services[name].statsError = error.message;
                }
            }
        }

        return status;
    }
}
// #endregion

// #region 全局服务管理器实例
let globalServiceManager = null;

/**
 * 获取全局服务管理器实例
 * @returns {ServiceManager} 服务管理器实例
 */
export function getServiceManager() {
    if (!globalServiceManager) {
        globalServiceManager = new ServiceManager();
        
        // 注册默认服务
        globalServiceManager.register('document', DocumentService);
        globalServiceManager.register('export', ExportService);
        globalServiceManager.register('nlp', NLPService);
        globalServiceManager.register('appInitializer', AppInitializer);
    }
    
    return globalServiceManager;
}

/**
 * 重置全局服务管理器
 */
export function resetServiceManager() {
    if (globalServiceManager) {
        globalServiceManager.destroyAll();
        globalServiceManager = null;
    }
}
// #endregion

// #region 便捷服务创建函数
/**
 * 创建文档服务实例
 * @param {Object} options - 配置选项
 * @returns {DocumentService} 文档服务实例
 */
export function createDocumentServiceInstance(options = {}) {
    return createDocumentService(options);
}

/**
 * 创建导出服务实例
 * @param {Object} options - 配置选项
 * @returns {ExportService} 导出服务实例
 */
export function createExportServiceInstance(options = {}) {
    return createExportService(options);
}

/**
 * 创建NLP服务实例
 * @param {Object} options - 配置选项
 * @returns {NLPService} NLP服务实例
 */
export function createNLPServiceInstance(options = {}) {
    return createNLPService(options);
}

/**
 * 创建应用初始化器实例
 * @param {Object} options - 配置选项
 * @returns {AppInitializer} 应用初始化器实例
 */
export function createAppInitializerInstance(options = {}) {
    return createAppInitializer(options);
}
// #endregion

// #region 主要导出
export {
    // 服务类
    DocumentService,
    ExportService,
    NLPService,
    AppInitializer,
    
    // 服务工厂函数
    createDocumentService,
    createExportService,
    createNLPService,
    createAppInitializer
};

// 默认导出服务管理器
export default ServiceManager;
// #endregion
