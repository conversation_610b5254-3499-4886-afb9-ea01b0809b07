/**
 * @file 表单数据管理器 - 处理表单数据填充和管理
 * 负责从NLP结果填充表单字段、服务项目管理、总金额计算等
 */

(function() {
    'use strict';

    /**
     * 表单数据管理器类
     * @class FormDataManager
     */
    class FormDataManager {
        constructor() {
            this.serviceItemsContainer = null;
            this.totalAmountDisplay = null;
            this.init();
        }

        /**
         * 初始化表单数据管理器
         * @function init
         */
        init() {
            this.serviceItemsContainer = document.getElementById('service-items-container');
            this.totalAmountDisplay = document.getElementById('total-amount-display');
            
            // 绑定事件监听器
            this._bindEventListeners();
        }

        /**
         * 绑定事件监听器
         * @function _bindEventListeners
         * @private
         */
        _bindEventListeners() {
            // 添加服务项目按钮
            const addServiceBtn = document.getElementById('add-service-item');
            if (addServiceBtn) {
                addServiceBtn.addEventListener('click', () => this.addServiceItem());
            }

            // 监听现有服务项目的变化
            if (this.serviceItemsContainer) {
                this.serviceItemsContainer.addEventListener('input', (e) => {
                    if (e.target.classList.contains('service-quantity') || 
                        e.target.classList.contains('service-unit-price')) {
                        this._updateServiceItemAmount(e.target);
                    }
                    this.updateTotalAmount();
                });

                this.serviceItemsContainer.addEventListener('click', (e) => {
                    if (e.target.classList.contains('remove-item') || 
                        e.target.closest('.remove-item')) {
                        this._removeServiceItem(e.target.closest('.service-item'));
                    }
                });
            }
        }

        /**
         * 从NLP结果填充表单字段
         * @function fillFormFromNLPResult
         * @param {Object} extractedData - 提取的数据
         */
        fillFormFromNLPResult(extractedData) {
            if (window.SmartOffice && window.SmartOffice.Logger) {
                window.SmartOffice.Logger.debug('FormDataManager', 'fillFormFromNLPResult', '开始填充表单字段', {
                    fieldsCount: Object.keys(extractedData).length,
                    extractedData: extractedData
                });
            }
            
            try {
                let filledFields = 0;
                
                // 填充客户名称
                filledFields += this._fillCustomerName(extractedData);
                
                // 填充单号
                filledFields += this._fillDocumentNumber(extractedData);
                
                // 填充日期
                filledFields += this._fillDate(extractedData);
                
                // 填充服务项目
                filledFields += this._fillServiceItems(extractedData);
                
                // 填充总金额
                filledFields += this._fillTotalAmount(extractedData);
                
                // 填充电话号码
                filledFields += this._fillPhoneNumber(extractedData);
                
                // 填充渠道信息
                filledFields += this._fillChannel(extractedData);
                
                // 填充备注信息
                filledFields += this._fillNotes(extractedData);
                
                // 根据文档类型填充特定字段
                const documentType = document.getElementById('document-type')?.value;
                filledFields += this._fillDocumentSpecificFields(extractedData, documentType);
                
                // 触发表单更新事件
                this._triggerFormUpdate();
                
                // 强制更新预览
                this._updatePreview();
                
                // 显示填充结果
                this._showFillResult(filledFields, extractedData);
                
            } catch (error) {
                if (window.SmartOffice && window.SmartOffice.Logger) {
                    window.SmartOffice.Logger.error('FormDataManager', 'fillFormFromNLPResult', '❌ 表单填充失败', { 
                        error: error.message,
                        stack: error.stack,
                        extractedData 
                    });
                }
                
                if (window.showErrorNotification) {
                    window.showErrorNotification('填充失败', `表单填充时发生错误：${error.message}`);
                }
            }
        }

        /**
         * 填充客户名称
         * @function _fillCustomerName
         * @param {Object} extractedData - 提取的数据
         * @returns {number} 填充的字段数量
         * @private
         */
        _fillCustomerName(extractedData) {
            const customerName = extractedData.customerName || extractedData.clientName || 
                               extractedData.buyerName || extractedData.driverName;
            
            if (customerName) {
                const customerNameInput = document.getElementById('customer-name');
                if (customerNameInput) {
                    customerNameInput.value = customerName;
                    this._triggerInputEvents(customerNameInput);
                    
                    if (window.SmartOffice && window.SmartOffice.Logger) {
                        window.SmartOffice.Logger.debug('FormDataManager', '_fillCustomerName', '✅ 客户名称已填充', { 
                            customerName,
                            currentValue: customerNameInput.value 
                        });
                    }
                    return 1;
                }
            }
            return 0;
        }

        /**
         * 填充文档编号
         * @function _fillDocumentNumber
         * @param {Object} extractedData - 提取的数据
         * @returns {number} 填充的字段数量
         * @private
         */
        _fillDocumentNumber(extractedData) {
            const number = extractedData.receiptNumber || extractedData.invoiceNumber || 
                          extractedData.quotationNumber || extractedData.agreementNumber;
            
            if (number) {
                const numberInput = document.getElementById('receipt-number-input');
                if (numberInput) {
                    numberInput.value = number;
                    
                    if (window.SmartOffice && window.SmartOffice.Logger) {
                        window.SmartOffice.Logger.debug('FormDataManager', '_fillDocumentNumber', '✅ 单号已填充', { number });
                    }
                    return 1;
                }
            }
            return 0;
        }

        /**
         * 填充日期
         * @function _fillDate
         * @param {Object} extractedData - 提取的数据
         * @returns {number} 填充的字段数量
         * @private
         */
        _fillDate(extractedData) {
            const date = extractedData.issueDate || extractedData.quotationDate || 
                        extractedData.signingDate || extractedData.date;
            
            if (date) {
                const dateInput = document.getElementById('receipt-date-input');
                if (dateInput) {
                    // 尝试解析日期格式
                    let dateValue = date;
                    if (typeof date === 'string') {
                        const parsedDate = new Date(date);
                        if (!isNaN(parsedDate.getTime())) {
                            dateValue = parsedDate.toISOString().split('T')[0];
                        }
                    }
                    dateInput.value = dateValue;
                    
                    if (window.SmartOffice && window.SmartOffice.Logger) {
                        window.SmartOffice.Logger.debug('FormDataManager', '_fillDate', '✅ 日期已填充', { date: dateValue });
                    }
                    return 1;
                }
            }
            return 0;
        }

        /**
         * 填充服务项目
         * @function _fillServiceItems
         * @param {Object} extractedData - 提取的数据
         * @returns {number} 填充的字段数量
         * @private
         */
        _fillServiceItems(extractedData) {
            const services = extractedData.services || extractedData.items;
            
            if (Array.isArray(services) && services.length > 0) {
                this.fillServiceItems(services, extractedData);
                
                if (window.SmartOffice && window.SmartOffice.Logger) {
                    window.SmartOffice.Logger.debug('FormDataManager', '_fillServiceItems', '✅ 服务项目已填充', { 
                        servicesCount: services.length 
                    });
                }
                return 1;
            } else if (extractedData.description) {
                // 如果没有服务数组，但有描述，创建一个服务项目
                const services = [extractedData.description];
                this.fillServiceItems(services, extractedData);
                
                if (window.SmartOffice && window.SmartOffice.Logger) {
                    window.SmartOffice.Logger.debug('FormDataManager', '_fillServiceItems', '✅ 从描述创建服务项目', { 
                        description: extractedData.description 
                    });
                }
                return 1;
            }
            return 0;
        }

        /**
         * 填充总金额
         * @function _fillTotalAmount
         * @param {Object} extractedData - 提取的数据
         * @returns {number} 填充的字段数量
         * @private
         */
        _fillTotalAmount(extractedData) {
            const amount = extractedData.totalAmount || extractedData.estimatedAmount || extractedData.amount;
            
            if (amount) {
                const numericAmount = parseFloat(amount) || 0;
                if (numericAmount > 0) {
                    this.updateTotalAmount(numericAmount);
                    
                    if (window.SmartOffice && window.SmartOffice.Logger) {
                        window.SmartOffice.Logger.debug('FormDataManager', '_fillTotalAmount', '✅ 总金额已填充', { 
                            amount: numericAmount 
                        });
                    }
                    return 1;
                }
            }
            return 0;
        }

        /**
         * 填充电话号码
         * @function _fillPhoneNumber
         * @param {Object} extractedData - 提取的数据
         * @returns {number} 填充的字段数量
         * @private
         */
        _fillPhoneNumber(extractedData) {
            const phone = (extractedData.phones && Array.isArray(extractedData.phones) && extractedData.phones.length > 0) 
                         ? extractedData.phones[0] 
                         : extractedData.phone;
            
            if (phone) {
                const buyerTelInput = document.getElementById('buyer-tel');
                if (buyerTelInput) {
                    buyerTelInput.value = phone;
                    this._triggerInputEvents(buyerTelInput);
                    
                    if (window.SmartOffice && window.SmartOffice.Logger) {
                        window.SmartOffice.Logger.debug('FormDataManager', '_fillPhoneNumber', '✅ 电话号码已填充', { 
                            phone,
                            currentValue: buyerTelInput.value 
                        });
                    }
                    return 1;
                }
            }
            return 0;
        }

        /**
         * 填充渠道信息
         * @function _fillChannel
         * @param {Object} extractedData - 提取的数据
         * @returns {number} 填充的字段数量
         * @private
         */
        _fillChannel(extractedData) {
            if (extractedData.channel) {
                const channelInput = document.getElementById('channel');
                if (channelInput) {
                    channelInput.value = extractedData.channel;
                    
                    if (window.SmartOffice && window.SmartOffice.Logger) {
                        window.SmartOffice.Logger.debug('FormDataManager', '_fillChannel', '✅ 渠道信息已填充', { 
                            channel: extractedData.channel 
                        });
                    }
                    return 1;
                }
            }
            return 0;
        }

        /**
         * 填充备注信息
         * @function _fillNotes
         * @param {Object} extractedData - 提取的数据
         * @returns {number} 填充的字段数量
         * @private
         */
        _fillNotes(extractedData) {
            if (extractedData.notes) {
                const notesInput = document.getElementById('notes');
                if (notesInput) {
                    notesInput.value = extractedData.notes;
                    
                    if (window.SmartOffice && window.SmartOffice.Logger) {
                        window.SmartOffice.Logger.debug('FormDataManager', '_fillNotes', '✅ 备注信息已填充', { 
                            notes: extractedData.notes 
                        });
                    }
                    return 1;
                }
            }
            return 0;
        }

        /**
         * 触发输入事件
         * @function _triggerInputEvents
         * @param {HTMLElement} element - 输入元素
         * @private
         */
        _triggerInputEvents(element) {
            element.dispatchEvent(new Event('change', { bubbles: true }));
            element.dispatchEvent(new Event('input', { bubbles: true }));
        }

        /**
         * 触发表单更新
         * @function _triggerFormUpdate
         * @private
         */
        _triggerFormUpdate() {
            const customerNameInput = document.getElementById('customer-name');
            if (customerNameInput) {
                this._triggerInputEvents(customerNameInput);
            }
        }

        /**
         * 更新预览
         * @function _updatePreview
         * @private
         */
        _updatePreview() {
            if (typeof updatePreview === 'function') {
                updatePreview();
            } else if (typeof updateDocumentPreview === 'function') {
                updateDocumentPreview();
            }
        }

        /**
         * 显示填充结果
         * @function _showFillResult
         * @param {number} filledFields - 填充的字段数量
         * @param {Object} extractedData - 提取的数据
         * @private
         */
        _showFillResult(filledFields, extractedData) {
            if (window.SmartOffice && window.SmartOffice.Logger) {
                window.SmartOffice.Logger.info('FormDataManager', 'fillFormFromNLPResult', '✅ 表单字段填充完成', {
                    filledFields,
                    totalFields: Object.keys(extractedData).length,
                    documentType: document.getElementById('document-type')?.value
                });
            }

            if (filledFields === 0) {
                if (window.SmartOffice && window.SmartOffice.Logger) {
                    window.SmartOffice.Logger.warn('FormDataManager', 'fillFormFromNLPResult', '⚠️ 没有填充任何表单字段', { extractedData });
                }

                if (window.showWarningNotification) {
                    window.showWarningNotification('填充提醒', '解析成功但未找到可填充的字段，请检查输入内容');
                }
            } else {
                if (window.showSuccessNotification) {
                    window.showSuccessNotification('字段填充成功', `已成功填充 ${filledFields} 个字段`);
                }
            }
        }

        /**
         * 填充文档特定字段
         * @function _fillDocumentSpecificFields
         * @param {Object} extractedData - 提取的数据
         * @param {string} documentType - 文档类型
         * @returns {number} 填充的字段数量
         * @private
         */
        _fillDocumentSpecificFields(extractedData, documentType) {
            let filledFields = 0;

            switch (documentType) {
                case 'invoice':
                    // 填充发票特定字段
                    if (extractedData.invoiceNumber) {
                        const invoiceNumberInput = document.getElementById('receipt-number-input');
                        if (invoiceNumberInput) {
                            invoiceNumberInput.value = extractedData.invoiceNumber;
                            filledFields++;
                        }
                    }
                    if (extractedData.taxRate) {
                        const taxRateInput = document.getElementById('tax-rate-input');
                        if (taxRateInput) {
                            taxRateInput.value = extractedData.taxRate;
                            filledFields++;
                        }
                    }
                    break;

                case 'quotation':
                    // 填充报价单特定字段
                    if (extractedData.quotationNumber) {
                        const quotationNumberInput = document.getElementById('receipt-number-input');
                        if (quotationNumberInput) {
                            quotationNumberInput.value = extractedData.quotationNumber;
                            filledFields++;
                        }
                    }
                    if (extractedData.validUntil) {
                        const validUntilInput = document.getElementById('valid-until-input');
                        if (validUntilInput) {
                            const validDate = new Date(extractedData.validUntil);
                            if (!isNaN(validDate.getTime())) {
                                validUntilInput.value = validDate.toISOString().split('T')[0];
                                filledFields++;
                            }
                        }
                    }
                    break;

                case 'driver_agreement':
                    // 填充司机协议特定字段
                    if (extractedData.agreementNumber) {
                        const agreementNumberInput = document.getElementById('receipt-number-input');
                        if (agreementNumberInput) {
                            agreementNumberInput.value = extractedData.agreementNumber;
                            filledFields++;
                        }
                    }
                    if (extractedData.driverPhone) {
                        const buyerTelInput = document.getElementById('buyer-tel');
                        if (buyerTelInput) {
                            buyerTelInput.value = extractedData.driverPhone;
                            filledFields++;
                        }
                    }
                    break;

                default:
                    // 收据默认处理
                    if (extractedData.receiptNumber) {
                        const receiptNumberInput = document.getElementById('receipt-number-input');
                        if (receiptNumberInput) {
                            receiptNumberInput.value = extractedData.receiptNumber;
                            filledFields++;
                        }
                    }
                    break;
            }

            return filledFields;
        }

        /**
         * 填充服务项目
         * @function fillServiceItems
         * @param {Array} services - 服务项目数组
         * @param {Object} extractedData - 完整的提取数据
         */
        fillServiceItems(services, extractedData) {
            if (!this.serviceItemsContainer) return;

            // 清空现有项目
            this.serviceItemsContainer.innerHTML = '';

            const amounts = extractedData.amounts || [];
            const quantities = extractedData.quantities || [];

            services.forEach((service, index) => {
                const serviceItem = document.createElement('div');
                serviceItem.className = 'service-item mb-2 flex items-center space-x-1';

                const quantity = quantities[index] || 1;
                const amount = amounts[index] || 0;
                const unitPrice = amount / quantity || 0;

                serviceItem.innerHTML = `
                    <input type="text" class="service-description flex-grow bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-l-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="项目描述" value="${this._escapeHtml(service)}">
                    <input type="number" class="service-quantity w-16 bg-gray-50 border border-gray-300 text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="数量" value="${quantity}" min="0">
                    <input type="number" class="service-unit-price w-24 bg-gray-50 border border-gray-300 text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="单价" value="${unitPrice.toFixed(2)}" min="0" step="0.01">
                    <input type="number" class="service-amount w-24 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-r-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="金额" value="${amount.toFixed(2)}" min="0" step="0.01">
                    <button type="button" class="remove-item ml-1 text-red-500 hover:text-red-700 p-2" title="删除项目">
                        <i class="fas fa-times"></i>
                    </button>
                `;

                this.serviceItemsContainer.appendChild(serviceItem);
            });

            // 更新总金额
            this.updateTotalAmount();
        }

        /**
         * 添加服务项目
         * @function addServiceItem
         * @param {string} description - 项目描述
         * @param {number} quantity - 数量
         * @param {number} unitPrice - 单价
         */
        addServiceItem(description = '', quantity = 1, unitPrice = 0) {
            if (!this.serviceItemsContainer) return;

            const serviceItem = document.createElement('div');
            serviceItem.className = 'service-item mb-2 flex items-center space-x-1';

            const amount = quantity * unitPrice;

            serviceItem.innerHTML = `
                <input type="text" class="service-description flex-grow bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-l-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="项目描述" value="${this._escapeHtml(description)}">
                <input type="number" class="service-quantity w-16 bg-gray-50 border border-gray-300 text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="数量" value="${quantity}" min="0">
                <input type="number" class="service-unit-price w-24 bg-gray-50 border border-gray-300 text-gray-900 text-sm focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="单价" value="${unitPrice.toFixed(2)}" min="0" step="0.01">
                <input type="number" class="service-amount w-24 bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-r-lg focus:ring-blue-500 focus:border-blue-500 block p-2.5" placeholder="金额" value="${amount.toFixed(2)}" min="0" step="0.01">
                <button type="button" class="remove-item ml-1 text-red-500 hover:text-red-700 p-2" title="删除项目">
                    <i class="fas fa-times"></i>
                </button>
            `;

            this.serviceItemsContainer.appendChild(serviceItem);
            this.updateTotalAmount();
        }

        /**
         * 移除服务项目
         * @function _removeServiceItem
         * @param {HTMLElement} serviceItem - 服务项目元素
         * @private
         */
        _removeServiceItem(serviceItem) {
            if (serviceItem) {
                serviceItem.remove();
                this.updateTotalAmount();
            }
        }

        /**
         * 更新服务项目金额
         * @function _updateServiceItemAmount
         * @param {HTMLElement} input - 输入元素
         * @private
         */
        _updateServiceItemAmount(input) {
            const serviceItem = input.closest('.service-item');
            if (!serviceItem) return;

            const quantityInput = serviceItem.querySelector('.service-quantity');
            const unitPriceInput = serviceItem.querySelector('.service-unit-price');
            const amountInput = serviceItem.querySelector('.service-amount');

            if (quantityInput && unitPriceInput && amountInput) {
                const quantity = parseFloat(quantityInput.value) || 0;
                const unitPrice = parseFloat(unitPriceInput.value) || 0;
                amountInput.value = (quantity * unitPrice).toFixed(2);
            }
        }

        /**
         * 更新总金额显示
         * @function updateTotalAmount
         * @param {number} amount - 指定金额（可选）
         */
        updateTotalAmount(amount = null) {
            if (!this.totalAmountDisplay) return;

            let total = amount;

            if (total === null) {
                // 计算所有服务项目的总金额
                total = 0;
                const serviceAmounts = document.querySelectorAll('.service-amount');
                serviceAmounts.forEach(input => {
                    total += parseFloat(input.value) || 0;
                });
            }

            this.totalAmountDisplay.textContent = total.toFixed(2);
        }

        /**
         * HTML转义
         * @function _escapeHtml
         * @param {string} text - 要转义的文本
         * @returns {string} 转义后的文本
         * @private
         */
        _escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        /**
         * 清理资源
         * @function cleanup
         */
        cleanup() {
            this.serviceItemsContainer = null;
            this.totalAmountDisplay = null;
        }
    }

    // 导出到全局命名空间
    if (!window.SmartOffice) {
        window.SmartOffice = {};
    }
    if (!window.SmartOffice.Components) {
        window.SmartOffice.Components = {};
    }

    window.SmartOffice.Components.FormDataManager = FormDataManager;

    // 兼容性：导出全局函数
    window.fillFormFromNLPResult = function(extractedData) {
        if (window.SmartOffice.Components.formDataManager) {
            window.SmartOffice.Components.formDataManager.fillFormFromNLPResult(extractedData);
        }
    };

    window.fillServiceItems = function(services, extractedData) {
        if (window.SmartOffice.Components.formDataManager) {
            window.SmartOffice.Components.formDataManager.fillServiceItems(services, extractedData);
        }
    };

    window.updateTotalAmount = function(amount) {
        if (window.SmartOffice.Components.formDataManager) {
            window.SmartOffice.Components.formDataManager.updateTotalAmount(amount);
        }
    };

})();
